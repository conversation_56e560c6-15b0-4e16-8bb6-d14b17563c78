import { useEffect, useState, useRef } from 'react';

interface WebRTCDebugProps {
  peerConnection: RTCPeerConnection | null;
  videoElement: HTMLVideoElement | null;
}

interface DebugStats {
  connection: {
    state: string;
    iceState: string;
    gatheringState: string;
  };
  video: {
    readyState: number;
    networkState: number;
    videoWidth: number;
    videoHeight: number;
    duration: number;
    currentTime: number;
    paused: boolean;
    ended: boolean;
    buffered: string;
  };
  tracks: Array<{
    kind: string;
    id: string;
    enabled: boolean;
    readyState: string;
    muted: boolean;
  }>;
  codecs: Array<{
    mimeType: string;
    payloadType: number;
    clockRate: number;
    sdpFmtpLine?: string;
  }>;
  stats: {
    bytesReceived: number;
    packetsReceived: number;
    packetsLost: number;
    framesDecoded: number;
    framesDropped: number;
    codecName: string;
  };
}

export function WebRTCDebug({ peerConnection, videoElement }: WebRTCDebugProps) {
  const [debugStats, setDebugStats] = useState<DebugStats | null>(null);
  const [isVisible, setIsVisible] = useState(false);
  const intervalRef = useRef<NodeJS.Timeout>();

  useEffect(() => {
    if (!peerConnection || !videoElement) {
      return;
    }

    const updateStats = async () => {
      try {
        const stats: DebugStats = {
          connection: {
            state: peerConnection.connectionState,
            iceState: peerConnection.iceConnectionState,
            gatheringState: peerConnection.iceGatheringState,
          },
          video: {
            readyState: videoElement.readyState,
            networkState: videoElement.networkState,
            videoWidth: videoElement.videoWidth,
            videoHeight: videoElement.videoHeight,
            duration: videoElement.duration || 0,
            currentTime: videoElement.currentTime,
            paused: videoElement.paused,
            ended: videoElement.ended,
            buffered: getBufferedRanges(videoElement),
          },
          tracks: [],
          codecs: [],
          stats: {
            bytesReceived: 0,
            packetsReceived: 0,
            packetsLost: 0,
            framesDecoded: 0,
            framesDropped: 0,
            codecName: 'unknown',
          },
        };

        // Get track info
        const stream = videoElement.srcObject as MediaStream;
        if (stream) {
          stats.tracks = stream.getTracks().map(track => ({
            kind: track.kind,
            id: track.id,
            enabled: track.enabled,
            readyState: track.readyState,
            muted: track.muted,
          }));
        }

        // Get codec info from transceivers
        peerConnection.getTransceivers().forEach(transceiver => {
          if (transceiver.receiver) {
            const params = transceiver.receiver.getParameters();
            params.codecs?.forEach(codec => {
              stats.codecs.push({
                mimeType: codec.mimeType,
                payloadType: codec.payloadType,
                clockRate: codec.clockRate,
                sdpFmtpLine: codec.sdpFmtpLine,
              });
            });
          }
        });

        // Get WebRTC stats
        const rtcStats = await peerConnection.getStats();
        rtcStats.forEach(report => {
          if (report.type === 'inbound-rtp' && report.kind === 'video') {
            stats.stats.bytesReceived = report.bytesReceived || 0;
            stats.stats.packetsReceived = report.packetsReceived || 0;
            stats.stats.packetsLost = report.packetsLost || 0;
            stats.stats.framesDecoded = report.framesDecoded || 0;
            stats.stats.framesDropped = report.framesDropped || 0;
            stats.stats.codecName = report.codecId || 'unknown';
          }
        });

        setDebugStats(stats);
      } catch (error) {
        console.error('Error updating debug stats:', error);
      }
    };

    // Update stats every 2 seconds
    intervalRef.current = setInterval(updateStats, 2000);
    updateStats(); // Initial update

    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, [peerConnection, videoElement]);

  const getBufferedRanges = (video: HTMLVideoElement): string => {
    const buffered = video.buffered;
    if (buffered.length === 0) return 'none';
    
    const ranges = [];
    for (let i = 0; i < buffered.length; i++) {
      ranges.push(`${buffered.start(i).toFixed(2)}-${buffered.end(i).toFixed(2)}`);
    }
    return ranges.join(', ');
  };

  const getReadyStateText = (state: number): string => {
    const states = ['HAVE_NOTHING', 'HAVE_METADATA', 'HAVE_CURRENT_DATA', 'HAVE_FUTURE_DATA', 'HAVE_ENOUGH_DATA'];
    return states[state] || `UNKNOWN(${state})`;
  };

  const getNetworkStateText = (state: number): string => {
    const states = ['NETWORK_EMPTY', 'NETWORK_IDLE', 'NETWORK_LOADING', 'NETWORK_NO_SOURCE'];
    return states[state] || `UNKNOWN(${state})`;
  };

  if (!debugStats) {
    return null;
  }

  return (
    <div className="fixed bottom-4 right-4 z-50">
      <button
        onClick={() => setIsVisible(!isVisible)}
        className="bg-blue-600 text-white px-3 py-2 rounded-lg text-sm hover:bg-blue-700 transition-colors"
      >
        {isVisible ? 'Hide' : 'Show'} Debug
      </button>
      
      {isVisible && (
        <div className="mt-2 bg-black bg-opacity-90 text-white p-4 rounded-lg text-xs font-mono max-w-md max-h-96 overflow-y-auto">
          <h3 className="text-sm font-bold mb-2 text-blue-400">WebRTC Debug Info</h3>
          
          {/* Connection State */}
          <div className="mb-3">
            <h4 className="text-yellow-400 font-semibold">Connection:</h4>
            <div>State: <span className="text-green-400">{debugStats.connection.state}</span></div>
            <div>ICE: <span className="text-green-400">{debugStats.connection.iceState}</span></div>
            <div>Gathering: <span className="text-green-400">{debugStats.connection.gatheringState}</span></div>
          </div>

          {/* Video Element State */}
          <div className="mb-3">
            <h4 className="text-yellow-400 font-semibold">Video Element:</h4>
            <div>Ready: <span className="text-green-400">{getReadyStateText(debugStats.video.readyState)}</span></div>
            <div>Network: <span className="text-green-400">{getNetworkStateText(debugStats.video.networkState)}</span></div>
            <div>Size: <span className="text-green-400">{debugStats.video.videoWidth}x{debugStats.video.videoHeight}</span></div>
            <div>Time: <span className="text-green-400">{debugStats.video.currentTime.toFixed(2)}s</span></div>
            <div>Paused: <span className="text-green-400">{debugStats.video.paused ? 'Yes' : 'No'}</span></div>
            <div>Buffered: <span className="text-green-400">{debugStats.video.buffered}</span></div>
          </div>

          {/* Tracks */}
          <div className="mb-3">
            <h4 className="text-yellow-400 font-semibold">Tracks ({debugStats.tracks.length}):</h4>
            {debugStats.tracks.map((track, i) => (
              <div key={i} className="ml-2">
                <span className="text-cyan-400">{track.kind}</span>: 
                <span className="text-green-400"> {track.readyState}</span>
                {track.muted && <span className="text-red-400"> (MUTED)</span>}
                {!track.enabled && <span className="text-red-400"> (DISABLED)</span>}
              </div>
            ))}
          </div>

          {/* Codecs */}
          <div className="mb-3">
            <h4 className="text-yellow-400 font-semibold">Codecs:</h4>
            {debugStats.codecs.map((codec, i) => (
              <div key={i} className="ml-2">
                <div><span className="text-cyan-400">{codec.mimeType}</span> (PT: {codec.payloadType})</div>
                {codec.sdpFmtpLine && (
                  <div className="text-gray-400 text-xs ml-2">Profile: {codec.sdpFmtpLine}</div>
                )}
              </div>
            ))}
          </div>

          {/* Stats */}
          <div className="mb-3">
            <h4 className="text-yellow-400 font-semibold">RTP Stats:</h4>
            <div>Bytes: <span className="text-green-400">{debugStats.stats.bytesReceived.toLocaleString()}</span></div>
            <div>Packets: <span className="text-green-400">{debugStats.stats.packetsReceived}</span></div>
            <div>Lost: <span className="text-red-400">{debugStats.stats.packetsLost}</span></div>
            <div>Decoded: <span className="text-green-400">{debugStats.stats.framesDecoded}</span></div>
            <div>Dropped: <span className="text-red-400">{debugStats.stats.framesDropped}</span></div>
          </div>
        </div>
      )}
    </div>
  );
}
