export interface Stream {
  path: string;
  name: string;
  active: boolean;
}

export type MessageType =
  | 'viewer_register'
  | 'viewer_offer'
  | 'viewer_ice_candidate'
  | 'streams_list'
  | 'stream_status_update'
  | 'webrtc_answer'
  | 'ice_candidate';

export interface WebSocketMessage {
  type: MessageType;
  data: any;
}

export interface ViewerOfferData {
  stream_path: string;
  sdp: string;
  viewer_id: string;
}

export interface WebRTCAnswerData {
  sdp: string;
}

export interface ICECandidateData {
  viewer_id: string;
  candidate: string;
  sdp_mid: string;
  sdp_mline_index: number;
}
