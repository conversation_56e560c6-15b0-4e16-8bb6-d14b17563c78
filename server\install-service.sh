#!/bin/bash

# WebRTC Signaling Server - Service Installation Script
# This script installs the webrtc-signaling server as a systemd service

set -e

SERVICE_NAME="webrtc-signaling"
SERVICE_FILE="/etc/systemd/system/${SERVICE_NAME}.service"
BINARY_NAME="server"
INSTALL_DIR="$(pwd)"
USER="$(whoami)"
GROUP="$(id -gn)"

echo "Installing WebRTC Signaling Server as systemd service..."

# Get the actual user who ran sudo (if sudo was used)
if [ -n "$SUDO_USER" ]; then
    USER="$SUDO_USER"
    GROUP="$(id -gn $SUDO_USER)"
fi

echo "Service will run as user: $USER (group: $GROUP)"

# Check if running as root
if [ "$EUID" -ne 0 ]; then
    echo "Error: This script must be run as root (use sudo)"
    exit 1
fi

# Check if binary exists
if [ ! -f "./${BINARY_NAME}" ]; then
    echo "Error: Binary '${BINARY_NAME}' not found in current directory"
    echo "Please run this script from the server directory where the binary is located"
    exit 1
fi

# Set permissions for current directory and binary
echo "Setting permissions for binary in current directory: $INSTALL_DIR"
chmod +x "./${BINARY_NAME}"

# Create systemd service file
echo "Creating systemd service file: $SERVICE_FILE"
cat > "$SERVICE_FILE" << EOF
[Unit]
Description=WebRTC Signaling Server
Documentation=https://github.com/your-org/webrtc-signaling
After=network.target
Wants=network.target

[Service]
Type=simple
User=$USER
Group=$GROUP
ExecStart=$INSTALL_DIR/$BINARY_NAME
Restart=always
RestartSec=5
TimeoutStopSec=30

# Environment
Environment=CAM_SERVER_PORT=8612
WorkingDirectory=$INSTALL_DIR

# Logging
StandardOutput=journal
StandardError=journal
SyslogIdentifier=$SERVICE_NAME

[Install]
WantedBy=multi-user.target
EOF

# Reload systemd and enable service
echo "Reloading systemd daemon..."
systemctl daemon-reload

echo "Enabling $SERVICE_NAME service..."
systemctl enable "$SERVICE_NAME"

echo "Starting $SERVICE_NAME service..."
systemctl start "$SERVICE_NAME"

# Check service status
echo ""
echo "Service installation completed!"
echo ""
echo "Service status:"
systemctl status "$SERVICE_NAME" --no-pager -l

echo ""
echo "Useful commands:"
echo "  Start service:   sudo systemctl start $SERVICE_NAME"
echo "  Stop service:    sudo systemctl stop $SERVICE_NAME"
echo "  Restart service: sudo systemctl restart $SERVICE_NAME"
echo "  Check status:    sudo systemctl status $SERVICE_NAME"
echo "  View logs:       sudo journalctl -u $SERVICE_NAME -f"
echo "  Remove service:  sudo ./remove-service.sh"