# Camera Streaming Project

Dự án streaming camera cho phép xem các stream từ MediaMTX qua WebRTC.

## Kiến trúc

- **Server**: Go + Fiber + WebSocket - quản lý kết nối giữa publisher và viewer
- **Publisher**: Go client - kết nối với MediaMTX và chuyển tiếp WebRTC
- **Viewer**: React SPA - giao diện web để xem stream

## Cài đặt và chạy

### 1. MediaMTX
MediaMTX cần được cài sẵn và chạy tại `http://localhost:8889`

### 2. Server
```bash
cd server

# Build cho platform hiện tại
# Windows
build.bat
server.exe

# Linux/Mac
chmod +x build.sh
./build.sh
./server

# Cross-platform build
# Từ Windows build cho Linux
build.bat linux

# Từ Linux/Mac build cho Windows
./build.sh windows

# Các option khác: mac, arm64
```

Server sẽ chạy tại `http://localhost:8080`

### 3. Publisher
```bash
cd publisher

# Build cho platform hiện tại
# Windows
build.bat
publisher.exe

# Linux/Mac
chmod +x build.sh
./build.sh
./publisher

# Cross-platform build
# Từ Windows build cho Linux
build.bat linux

# Từ Linux/Mac build cho Windows
./build.sh windows

# Các option khác: mac, arm64
```

### 4. Viewer
```bash
cd viewer
npm install
npm run build
npm run start
```

Viewer sẽ chạy tại `http://localhost:3000`

## Cấu hình

### Server
- Biến môi trường:
  - `CAM_SERVER_PORT`: Port server (mặc định: `8080`)
- WebSocket endpoint: `/ws`
- Health check: `/health`

### Publisher
- Biến môi trường:
  - `CAM_SERVER_URL`: URL server WebSocket (mặc định: `ws://localhost:8080/ws`)
  - `MEDIAMTX_URL`: URL MediaMTX API (mặc định: `http://localhost:8889`)

### Viewer
- WebSocket tự động kết nối tới `ws://localhost:8080/ws`
- Có thể thay đổi trong `app/routes/home.tsx`
- Biến môi trường:
  - `CAM_VIEWER_PORT`: Port viewer server (mặc định: `3000`)

## Luồng hoạt động

1. **Publisher** kết nối WebSocket tới **Server** và đăng ký các stream
2. **Viewer** kết nối WebSocket tới **Server** và nhận danh sách stream
3. Người dùng chọn stream trên **Viewer**
4. **Viewer** tạo WebRTC offer và gửi qua **Server**
5. **Server** chuyển offer tới **Publisher** quản lý stream đó
6. **Publisher** gọi MediaMTX WHEP API để tạo session và nhận answer
7. **Publisher** gửi answer về **Viewer** qua **Server**
8. **Viewer** nhận answer và bắt đầu stream video

## Stream mặc định

Hiện tại hệ thống có sẵn 1 stream hardcode:
- Path: `stream-1`
- Tên: `Cam cân heo`

## Log

Tất cả component đều có logging chi tiết:
- Server: Zap logger với structured logging
- Publisher: Zap logger với structured logging
- Viewer: Console logging cho debug

## API WebSocket

### Publisher → Server
- `publisher_register`: Đăng ký làm publisher
- `publisher_streams`: Gửi danh sách stream quản lý
- `publisher_answer`: Gửi WebRTC answer

### Viewer → Server
- `viewer_register`: Đăng ký làm viewer
- `viewer_offer`: Gửi WebRTC offer

### Server → Viewer
- `streams_list`: Danh sách stream và trạng thái
- `webrtc_answer`: WebRTC answer từ publisher

### Server → Publisher
- `webrtc_offer`: WebRTC offer từ viewer

## Ví dụ sử dụng Environment Variables

### Server
```bash
# Windows
set CAM_SERVER_PORT=9090 && server.exe

# Linux/Mac
CAM_SERVER_PORT=9090 ./server
```

### Publisher
```bash
# Windows
set CAM_SERVER_URL=ws://localhost:9090/ws && publisher.exe

# Linux/Mac
CAM_SERVER_URL=ws://localhost:9090/ws ./publisher
```

### Viewer
```bash
# Windows
set CAM_VIEWER_PORT=4000 && npm run start

# Linux/Mac
CAM_VIEWER_PORT=4000 npm run start
```