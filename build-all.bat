@echo off
echo Building Camera Streaming System with Trickle ICE Support...
echo.

echo [1/3] Building Publisher...
cd publisher
go mod tidy
go build -o publisher.exe .
if %errorlevel% neq 0 (
    echo Failed to build publisher!
    exit /b 1
)
echo Publisher built successfully!
cd ..

echo.
echo [2/3] Building Server...
cd server
go mod tidy
go build -o server.exe .
if %errorlevel% neq 0 (
    echo Failed to build server!
    exit /b 1
)
echo Server built successfully!
cd ..

echo.
echo [3/3] Building Viewer...
cd viewer
call npm install
if %errorlevel% neq 0 (
    echo Failed to install viewer dependencies!
    exit /b 1
)
call npm run build
if %errorlevel% neq 0 (
    echo Failed to build viewer!
    exit /b 1
)
echo Viewer built successfully!
cd ..

echo.
echo ========================================
echo All components built successfully!
echo ========================================
echo.
echo To start the system:
echo 1. Start MediaMTX: .\publisher\mediamtx.exe
echo 2. Start Server: .\server\server.exe
echo 3. Start Publisher: .\publisher\publisher.exe
echo 4. Start Viewer: cd viewer ^&^& npm run start
echo.
echo Trickle ICE Features:
echo - Publisher uses WebRTC proxy for dual connections
echo - Viewer sends ICE candidates immediately
echo - Server routes ICE candidates between components
echo - MediaMTX connection uses vanilla ICE (local, fast)
echo - Viewer connection uses trickle ICE (remote, optimized)