@echo off
echo Building publisher...
go mod tidy

if "%1"=="" (
    echo Building for Windows...
    go build -o publisher.exe .
    echo Publisher built successfully as publisher.exe
) else if "%1"=="linux" (
    echo Building for Linux...
    set GOOS=linux
    set GOARCH=amd64
    go build -o publisher .
    echo Publisher built successfully as publisher ^(Linux^)
) else if "%1"=="mac" (
    echo Building for macOS...
    set GOOS=darwin
    set GOARCH=amd64
    go build -o publisher .
    echo Publisher built successfully as publisher ^(macOS^)
) else if "%1"=="arm64" (
    echo Building for Linux ARM64...
    set GOOS=linux
    set GOARCH=arm64
    go build -o publisher .
    echo Publisher built successfully as publisher ^(Linux ARM64^)
) else (
    echo Usage: build.bat [linux^|mac^|arm64]
    echo   No argument: Build for Windows
    echo   linux:       Build for Linux x64
    echo   mac:         Build for macOS x64
    echo   arm64:       Build for Linux ARM64
)