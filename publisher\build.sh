#!/bin/bash
echo "Building publisher..."
go mod tidy

if [ -z "$1" ]; then
    echo "Building for current platform..."
    go build -o publisher .
    echo "Publisher built successfully as publisher"
elif [ "$1" = "linux" ]; then
    echo "Building for Linux..."
    GOOS=linux GOARCH=amd64 go build -o publisher .
    echo "Publisher built successfully as publisher (Linux)"
elif [ "$1" = "windows" ]; then
    echo "Building for Windows..."
    GOOS=windows GOARCH=amd64 go build -o publisher.exe .
    echo "Publisher built successfully as publisher.exe (Windows)"
elif [ "$1" = "mac" ]; then
    echo "Building for macOS..."
    GOOS=darwin GOARCH=amd64 go build -o publisher .
    echo "Publisher built successfully as publisher (macOS)"
elif [ "$1" = "arm64" ]; then
    echo "Building for Linux ARM64..."
    GOOS=linux GOARCH=arm64 go build -o publisher .
    echo "Publisher built successfully as publisher (Linux ARM64)"
else
    echo "Usage: ./build.sh [linux|windows|mac|arm64]"
    echo "  No argument: Build for current platform"
    echo "  linux:       Build for Linux x64"
    echo "  windows:     Build for Windows x64"
    echo "  mac:         Build for macOS x64"
    echo "  arm64:       Build for Linux ARM64"
fi