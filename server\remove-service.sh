#!/bin/bash

# WebRTC Signaling Server - Service Removal <PERSON>t
# This script removes the webrtc-signaling server systemd service

set -e

SERVICE_NAME="webrtc-signaling"
SERVICE_FILE="/etc/systemd/system/${SERVICE_NAME}.service"
INSTALL_DIR="$(pwd)"

echo "Removing WebRTC Signaling Server systemd service..."

# Check if running as root
if [ "$EUID" -ne 0 ]; then
    echo "Error: This script must be run as root (use sudo)"
    exit 1
fi

# Stop service if running
if systemctl is-active --quiet "$SERVICE_NAME"; then
    echo "Stopping $SERVICE_NAME service..."
    systemctl stop "$SERVICE_NAME"
fi

# Disable service if enabled
if systemctl is-enabled --quiet "$SERVICE_NAME"; then
    echo "Disabling $SERVICE_NAME service..."
    systemctl disable "$SERVICE_NAME"
fi

# Remove service file
if [ -f "$SERVICE_FILE" ]; then
    echo "Removing service file: $SERVICE_FILE"
    rm -f "$SERVICE_FILE"
fi

# Reload systemd daemon
echo "Reloading systemd daemon..."
systemctl daemon-reload
systemctl reset-failed

# Note: Installation directory is the current working directory, so we don't remove it
echo "Note: Service was installed from current directory ($INSTALL_DIR)"
echo "Binary and scripts remain in place for future use."

echo ""
echo "Service removal completed!"
echo ""
echo "The $SERVICE_NAME service has been completely removed from the system."