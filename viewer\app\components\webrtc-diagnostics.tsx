import { useEffect, useState } from 'react';

interface WebRTCDiagnostics {
  browserSupport: {
    webrtc: boolean;
    h264: boolean;
    vp8: boolean;
    vp9: boolean;
  };
  policies: {
    autoplay: string;
    mediaDevices: boolean;
  };
  capabilities: {
    video: RTCRtpCodecCapability[];
    audio: RTCRtpCodecCapability[];
  };
}

export function WebRTCDiagnostics() {
  const [diagnostics, setDiagnostics] = useState<WebRTCDiagnostics | null>(null);
  const [testResults, setTestResults] = useState<string[]>([]);

  useEffect(() => {
    runDiagnostics();
  }, []);

  const runDiagnostics = async () => {
    const results: string[] = [];
    
    try {
      // Test WebRTC support
      const webrtcSupported = !!(window.RTCPeerConnection || window.webkitRTCPeerConnection);
      results.push(`WebRTC Support: ${webrtcSupported ? '✅' : '❌'}`);

      // Test codec capabilities
      const videoCapabilities = RTCRtpReceiver.getCapabilities('video');
      const audioCapabilities = RTCRtpReceiver.getCapabilities('audio');
      
      const h264Support = videoCapabilities?.codecs.some(c => c.mimeType.includes('H264')) || false;
      const vp8Support = videoCapabilities?.codecs.some(c => c.mimeType.includes('VP8')) || false;
      const vp9Support = videoCapabilities?.codecs.some(c => c.mimeType.includes('VP9')) || false;
      
      results.push(`H.264 Support: ${h264Support ? '✅' : '❌'}`);
      results.push(`VP8 Support: ${vp8Support ? '✅' : '❌'}`);
      results.push(`VP9 Support: ${vp9Support ? '✅' : '❌'}`);

      // Test autoplay policy
      const video = document.createElement('video');
      video.muted = true;
      video.autoplay = true;
      video.playsInline = true;
      
      try {
        await video.play();
        results.push('Autoplay Policy: ✅ Allowed');
      } catch (e) {
        results.push('Autoplay Policy: ❌ Blocked');
        results.push(`Autoplay Error: ${e}`);
      }

      // Test media devices access
      try {
        const devices = await navigator.mediaDevices.enumerateDevices();
        results.push(`Media Devices: ✅ ${devices.length} devices`);
      } catch (e) {
        results.push(`Media Devices: ❌ ${e}`);
      }

      // Test track muting behavior
      results.push('\n🔍 Testing Track Muting Behavior:');
      
      // Create test peer connection
      const pc = new RTCPeerConnection();
      
      // Add transceiver and check default state
      const transceiver = pc.addTransceiver('video', { direction: 'recvonly' });
      results.push(`Default transceiver direction: ${transceiver.direction}`);
      
      // Test track creation
      const canvas = document.createElement('canvas');
      canvas.width = 640;
      canvas.height = 480;
      const ctx = canvas.getContext('2d');
      if (ctx) {
        ctx.fillStyle = 'red';
        ctx.fillRect(0, 0, 640, 480);
      }
      
      const stream = canvas.captureStream(30);
      const track = stream.getVideoTracks()[0];
      
      results.push(`Test track muted: ${track.muted}`);
      results.push(`Test track enabled: ${track.enabled}`);
      results.push(`Test track readyState: ${track.readyState}`);
      
      pc.close();

      setDiagnostics({
        browserSupport: {
          webrtc: webrtcSupported,
          h264: h264Support,
          vp8: vp8Support,
          vp9: vp9Support,
        },
        policies: {
          autoplay: 'tested',
          mediaDevices: true,
        },
        capabilities: {
          video: videoCapabilities?.codecs || [],
          audio: audioCapabilities?.codecs || [],
        },
      });

      setTestResults(results);

    } catch (error) {
      results.push(`❌ Diagnostics failed: ${error}`);
      setTestResults(results);
    }
  };

  const testTrackMutingFix = async () => {
    const results = [...testResults];
    results.push('\n🔧 Testing Track Muting Fixes:');
    
    try {
      // Test 1: Create unmuted track
      const canvas = document.createElement('canvas');
      canvas.width = 320;
      canvas.height = 240;
      const ctx = canvas.getContext('2d');
      if (ctx) {
        ctx.fillStyle = 'green';
        ctx.fillRect(0, 0, 320, 240);
      }
      
      const stream = canvas.captureStream(30);
      const track = stream.getVideoTracks()[0];
      
      results.push(`Created track muted: ${track.muted}`);
      
      // Test 2: Force mute and unmute
      track.enabled = false;
      results.push(`After disable - muted: ${track.muted}, enabled: ${track.enabled}`);
      
      track.enabled = true;
      results.push(`After enable - muted: ${track.muted}, enabled: ${track.enabled}`);
      
      // Test 3: Clone track
      const clonedTrack = track.clone();
      results.push(`Cloned track muted: ${clonedTrack.muted}`);
      
      // Test 4: MediaStream operations
      const newStream = new MediaStream([track]);
      results.push(`New stream active: ${newStream.active}`);
      results.push(`New stream tracks: ${newStream.getTracks().length}`);
      
      track.stop();
      clonedTrack.stop();
      
    } catch (error) {
      results.push(`❌ Track testing failed: ${error}`);
    }
    
    setTestResults(results);
  };

  if (!diagnostics) {
    return <div className="p-4 bg-gray-800 text-white">Running WebRTC diagnostics...</div>;
  }

  return (
    <div className="p-4 bg-gray-800 text-white text-sm">
      <h3 className="text-lg font-bold mb-4">🔍 WebRTC Diagnostics</h3>
      
      <div className="mb-4">
        <h4 className="font-semibold mb-2">Test Results:</h4>
        <div className="bg-gray-900 p-2 rounded font-mono text-xs max-h-96 overflow-y-auto">
          {testResults.map((result, index) => (
            <div key={index}>{result}</div>
          ))}
        </div>
      </div>
      
      <div className="mb-4">
        <h4 className="font-semibold mb-2">Video Codecs:</h4>
        <div className="bg-gray-900 p-2 rounded text-xs max-h-32 overflow-y-auto">
          {diagnostics.capabilities.video.map((codec, index) => (
            <div key={index}>{codec.mimeType} - {codec.sdpFmtpLine || 'no params'}</div>
          ))}
        </div>
      </div>
      
      <button
        onClick={testTrackMutingFix}
        className="px-3 py-1 bg-blue-600 rounded text-sm hover:bg-blue-500"
      >
        🧪 Test Track Muting Fixes
      </button>
    </div>
  );
}
