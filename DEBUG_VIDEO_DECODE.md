# 🔧 Video Decode Issues Debug Guide

## 🎯 Problem Description
Viewer nhận được video track từ WebRTC nhưng không thể decode được, dẫn đến video element không hiển thị video.

## 🔍 Common Causes

### 1. **Codec Mismatch**
- MediaMTX gửi codec khác với những gì browser hỗ trợ
- H.264 profile incompatibility
- Missing SPS/PPS parameters

### 2. **Payload Type Mapping Issues**
- RTP payload type bị map sai giữa MediaMTX và viewer
- Dynamic payload type conflicts

### 3. **Video Element State Issues**
- Video metadata không được load
- Dimensions = 0x0
- ReadyState stuck at low values

## 🛠️ Debug Tools

### 1. **Enhanced Viewer Debug**
```bash
# Start with debug tools
.\debug-and-test.bat
```

- Mở browser tới `http://localhost:3000`
- Click "Show Debug" button ở góc dưới phải
- Monitor real-time WebRTC stats

### 2. **Codec Compatibility Check**
```bash
node debug-codec-mismatch.js
```

### 3. **Browser Console Logs**
Kiểm tra các log sau trong browser console:
- `=== BROWSER CODEC CAPABILITIES ===`
- `=== TRACK EVENT DETAILS ===`
- `=== TRANSCEIVER STATES ===`
- `=== VIDEO ELEMENT STATE ===`

### 4. **Publisher Logs**
Kiểm tra các log sau trong publisher terminal:
- `🎬 RECEIVED TRACK FROM MEDIAMTX`
- `📹 H.264 TRACK DETAILS`
- `📦 RTP PACKET FORWARDED`
- `🚨 CRITICAL CODEC MISMATCH DETECTED`

## 🔧 Troubleshooting Steps

### Step 1: Verify MediaMTX Status
```bash
curl http://localhost:8889/v3/paths/list
```
Kiểm tra:
- Stream có `sourceReady: true`
- Track codec information

### Step 2: Check Browser Codec Support
Trong browser console, kiểm tra:
```javascript
// Video codecs
RTCRtpReceiver.getCapabilities('video').codecs.forEach(c => 
  console.log(c.mimeType, c.sdpFmtpLine)
);
```

### Step 3: Monitor Video Element State
Kiểm tra trong debug panel:
- **ReadyState**: Should progress to `HAVE_ENOUGH_DATA` (4)
- **NetworkState**: Should be `NETWORK_IDLE` (1)
- **Dimensions**: Should show actual video size (not 0x0)
- **Tracks**: Should show active video/audio tracks

### Step 4: Analyze RTP Packets
Trong publisher logs, tìm:
- `slice_idr` packets (key frames)
- `sps` và `pps` packets (critical for H.264 decode)
- Continuous packet flow without errors

### Step 5: Check H.264 Profile Compatibility
Tìm trong logs:
- MediaMTX profile: `📹 H.264 TRACK DETAILS`
- Browser profile support
- Profile matching warnings

## 🎯 Common Solutions

### 1. **H.264 Profile Mismatch**
```yaml
# In mediamtx.yml, force compatible profile
paths:
  stream-1:
    source: rtsp://...
    # Add transcoding to compatible profile
    runOnDemand: ffmpeg -i rtsp://... -c:v libx264 -profile:v baseline -level 3.1 -f rtsp rtsp://localhost:8554/stream-1
```

### 2. **Force VP8/VP9 Codec**
Modify viewer to prefer VP8/VP9 over H.264:
```javascript
// In stream-viewer.tsx, modify transceiver creation
pc.addTransceiver('video', { 
  direction: 'recvonly',
  // Force VP8
  codecs: RTCRtpReceiver.getCapabilities('video').codecs.filter(c => 
    c.mimeType.includes('VP8')
  )
});
```

### 3. **MediaMTX Transcoding**
Enable transcoding in MediaMTX config:
```yaml
# Force H.264 baseline profile
runOnDemand: ffmpeg -i $RTSP_PATH -c:v libx264 -profile:v baseline -level 3.1 -preset ultrafast -tune zerolatency -f rtsp rtsp://localhost:8554/$MTX_PATH
```

### 4. **Browser Compatibility**
Test with different browsers:
- Chrome: Best H.264 support
- Firefox: Good VP8/VP9 support
- Safari: Limited codec support

## 📊 Debug Checklist

- [ ] MediaMTX running and stream active
- [ ] Server WebSocket connected
- [ ] Publisher connected to MediaMTX
- [ ] Viewer WebSocket connected
- [ ] WebRTC offer/answer exchange successful
- [ ] ICE connection established
- [ ] Track events received in browser
- [ ] Video element has srcObject set
- [ ] Video dimensions > 0x0
- [ ] ReadyState progresses to 4
- [ ] No codec mismatch warnings
- [ ] RTP packets flowing (check packet count)
- [ ] H.264 SPS/PPS packets received
- [ ] No browser video errors

## 🚨 Critical Indicators

### ❌ **Decode Failure Signs**
- Video dimensions remain 0x0
- ReadyState stuck at 0-1
- No `onLoadedMetadata` event
- Browser video error events
- Missing SPS/PPS in RTP logs

### ✅ **Successful Decode Signs**
- Video dimensions show actual size
- ReadyState reaches 4 (HAVE_ENOUGH_DATA)
- `onLoadedMetadata` and `onCanPlay` events fire
- Continuous RTP packet flow
- H.264 key frames (slice_idr) received

## 📞 Advanced Debugging

### WebRTC Stats API
```javascript
// Get detailed stats
pc.getStats().then(stats => {
  stats.forEach(report => {
    if (report.type === 'inbound-rtp' && report.kind === 'video') {
      console.log('Video stats:', {
        bytesReceived: report.bytesReceived,
        framesDecoded: report.framesDecoded,
        framesDropped: report.framesDropped,
        codecName: report.codecId
      });
    }
  });
});
```

### MediaMTX API Monitoring
```bash
# Monitor stream status
watch -n 1 'curl -s http://localhost:8889/v3/paths/get/stream-1 | jq'
```

### Network Analysis
```bash
# Monitor WebRTC traffic
netstat -an | findstr :8080
netstat -an | findstr :8889
```
