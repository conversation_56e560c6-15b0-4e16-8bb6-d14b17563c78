{"name": "viewer", "private": true, "type": "module", "scripts": {"build": "react-router build", "dev": "react-router dev --host", "start": "node serve.js", "typecheck": "react-router typegen && tsc", "format": "prettier --write app/"}, "dependencies": {"@react-router/node": "^7.7.1", "@react-router/serve": "^7.7.1", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "express": "^4.18.2", "isbot": "^5.1.27", "jotai": "^2.14.0", "lucide-react": "^0.544.0", "prettier": "^3.6.2", "react": "^19.1.0", "react-dom": "^19.1.0", "react-router": "^7.7.1", "tailwind-merge": "^3.3.1"}, "devDependencies": {"@react-router/dev": "^7.7.1", "@tailwindcss/vite": "^4.1.4", "@types/node": "^20", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "tailwindcss": "^4.1.4", "tw-animate-css": "^1.3.8", "typescript": "^5.8.3", "vite": "^6.3.3", "vite-tsconfig-paths": "^5.1.4"}}