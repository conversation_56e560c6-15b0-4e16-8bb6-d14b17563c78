# Technology Stack

## Backend (Server)
- **Language**: Go 1.21
- **Web Framework**: Fiber v2.52.5
- **WebSocket**: gofiber/websocket v2.2.1
- **Logging**: Zap (structured logging)
- **UUID**: google/uuid v1.6.0

## Backend (Publisher)
- **Language**: Go 1.21
- **WebSocket**: gorilla/websocket v1.5.1
- **Logging**: Zap (structured logging)

## Frontend (Viewer)
- **Framework**: React Router v7.7.1
- **React Version**: 19.1.0
- **Language**: TypeScript 5.8.3
- **Build Tool**: Vite 6.3.3
- **Styling**: TailwindCSS v4.1.4
- **State Management**: Jotai v2.14.0
- **Icons**: Lucide React v0.544.0
- **Utilities**: 
  - class-variance-authority v0.7.1
  - clsx v2.1.1
  - tailwind-merge v3.3.1
- **Server**: Express.js 4.18.2 (for production serving)

## Development Tools
- **TypeScript**: Strict configuration
- **Prettier**: Code formatting with custom config
- **Docker**: Multi-stage builds for all components
- **Cross-platform builds**: Windows, Linux, macOS, ARM64 support

## External Dependencies
- **MediaMTX**: Required for WHEP API and stream management
- **WebRTC**: For peer-to-peer video streaming