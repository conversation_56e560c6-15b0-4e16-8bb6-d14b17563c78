@echo off
echo 🔨 Quick Build - Fixing Muted Video Track Issue...

echo Building publisher...
cd publisher
go build -o publisher.exe .
if %ERRORLEVEL% neq 0 (
    echo ❌ Publisher build failed!
    pause
    exit /b 1
)
echo ✅ Publisher built successfully

cd ..
echo.
echo 🚀 Ready to test REAL muted video fixes!
echo.
echo 📋 Test Steps:
echo 1. Restart publisher: cd publisher && publisher.exe
echo 2. Refresh viewer page
echo 3. Look for muted track warnings in console
echo 4. Try "🔧 Fix Muted Video" button if needed
echo 5. Monitor debug panel for track states
echo.
echo 🔧 NEW FIXES IMPLEMENTED:
echo ✅ Real track unmuting (not just logging)
echo ✅ Track cloning to get unmuted versions
echo ✅ Video element muted=false
echo ✅ Automatic mute detection every 3s
echo ✅ Aggressive manual fix button
echo ✅ WebSocket race condition fixed
echo ✅ Nil pointer dereference fixes
echo ✅ Panic recovery in RTP forwarding
echo ✅ Callback safety with value capture
echo.
echo Expected results:
echo ✅ video: live (NOT MUTED)
echo ✅ Video dimensions > 0x0
echo ✅ Frames decoded > 0
echo ✅ Video actually plays!
echo.
pause
