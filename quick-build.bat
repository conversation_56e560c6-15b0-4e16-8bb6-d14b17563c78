@echo off
echo 🔨 Quick Build - Fixing WebSocket Race Condition...

echo Building publisher...
cd publisher
go build -o publisher.exe .
if %ERRORLEVEL% neq 0 (
    echo ❌ Publisher build failed!
    pause
    exit /b 1
)
echo ✅ Publisher built successfully

cd ..
echo.
echo 🚀 Ready to test! 
echo.
echo 📋 Test Steps:
echo 1. Restart publisher: cd publisher && publisher.exe
echo 2. Refresh viewer page
echo 3. Check for "Fix Muted Video" button
echo 4. Monitor debug panel for track states
echo.
echo Expected fixes:
echo ✅ No more WebSocket panic
echo ✅ Video track not muted
echo ✅ Video dimensions > 0x0
echo ✅ Frames decoded > 0
echo.
pause
