# Suggested Development Commands

## Windows System Commands
- `dir` - List directory contents
- `cd` - Change directory
- `type` - Read file contents
- `find` - Search text in files
- `copy` - Copy files
- `del` - Delete files
- `md` - Create directory

## Git Commands
- `git status` - Check repository status
- `git add .` - Stage all changes
- `git commit -m "message"` - Commit changes
- `git push` - Push to remote
- `git pull` - Pull from remote

## Server (Go)
```bash
cd server

# Development
go run main.go                    # Run in development
go mod tidy                       # Clean dependencies

# Building
build.bat                         # Build for Windows
build.bat linux                  # Cross-compile for Linux
build.bat mac                    # Cross-compile for macOS
build.bat arm64                  # Cross-compile for ARM64

# Running
server.exe                        # Run Windows executable
```

## Publisher (Go)
```bash
cd publisher

# Development
go run main.go                    # Run in development
go mod tidy                       # Clean dependencies

# Building
build.bat                         # Build for Windows
build.bat linux                  # Cross-compile for Linux
build.bat mac                    # Cross-compile for macOS
build.bat arm64                  # Cross-compile for ARM64

# Running
publisher.exe                     # Run Windows executable
```

## Viewer (React Router)
```bash
cd viewer

# Development
npm install                       # Install dependencies
npm run dev                       # Start dev server (http://localhost:5173)

# Code Quality
npm run typecheck                 # TypeScript type checking
npm run format                    # Format code with Prettier

# Building & Production
npm run build                     # Create production build
npm run start                     # Start production server (http://localhost:3000)

# Docker
docker build -t cam-streaming-viewer .
docker run -p 3000:3000 cam-streaming-viewer
```

## Environment Variables
### Server
- `CAM_SERVER_PORT=8080` - Server port (default: 8080)

### Publisher
- `CAM_SERVER_URL=ws://localhost:8080/ws` - WebSocket server URL
- `MEDIAMTX_URL=http://localhost:8889` - MediaMTX API URL

### Viewer
- `CAM_VIEWER_PORT=3000` - Viewer server port (default: 3000)

## Testing MediaMTX
Ensure MediaMTX is running at `http://localhost:8889` before starting other components.