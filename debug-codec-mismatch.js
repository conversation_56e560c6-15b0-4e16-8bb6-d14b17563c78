#!/usr/bin/env node

/**
 * Debug script to check codec compatibility between MediaMTX and browser
 * Run this script to diagnose video decode issues
 */

const http = require('http');
const https = require('https');

// MediaMTX API endpoint
const MEDIAMTX_API = 'http://localhost:8889/v3/paths/list';
const MEDIAMTX_WHEP = 'http://localhost:8889/stream-1/whep';

// Test browser codec capabilities (simulated)
const BROWSER_CODECS = {
  video: [
    'video/VP8',
    'video/VP9', 
    'video/H264',
    'video/AV1'
  ],
  audio: [
    'audio/opus',
    'audio/PCMU',
    'audio/PCMA'
  ]
};

// H.264 profiles commonly supported by browsers
const BROWSER_H264_PROFILES = [
  '42e01f', // Baseline Level 3.1
  '42001f', // Baseline Level 3.1
  '640c1f', // High Level 3.1
  '42801f', // Baseline Level 3.1
];

async function checkMediaMTXStatus() {
  console.log('🔍 Checking MediaMTX status...');
  
  try {
    const response = await makeRequest(MEDIAMTX_API);
    const data = JSON.parse(response);
    
    console.log('✅ MediaMTX is running');
    console.log('📊 Available paths:');
    
    if (data.items) {
      data.items.forEach(path => {
        console.log(`  - ${path.name}: ${path.sourceReady ? '🟢 Ready' : '🔴 Not Ready'}`);
        if (path.tracks && path.tracks.length > 0) {
          path.tracks.forEach(track => {
            console.log(`    Track: ${track.codec || 'unknown codec'}`);
          });
        }
      });
    }
    
    return true;
  } catch (error) {
    console.error('❌ MediaMTX not accessible:', error.message);
    return false;
  }
}

async function testWHEPOffer() {
  console.log('\n🧪 Testing WHEP offer with browser-like SDP...');
  
  // Create a test SDP offer that mimics what a browser would send
  const testOffer = createTestSDP();
  
  try {
    const response = await makeRequest(MEDIAMTX_WHEP, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/sdp',
        'Accept': 'application/sdp'
      },
      body: testOffer
    });
    
    console.log('✅ WHEP endpoint responded');
    console.log('📄 MediaMTX Answer SDP:');
    console.log('---');
    console.log(response);
    console.log('---');
    
    analyzeSDPAnswer(response);
    
  } catch (error) {
    console.error('❌ WHEP test failed:', error.message);
  }
}

function createTestSDP() {
  return `v=0
o=- 123456789 2 IN IP4 127.0.0.1
s=-
t=0 0
a=group:BUNDLE 0 1
a=extmap-allow-mixed
a=msid-semantic: WMS
m=video 9 UDP/TLS/RTP/SAVPF 96 97 98 99
c=IN IP4 0.0.0.0
a=rtcp:9 IN IP4 0.0.0.0
a=ice-ufrag:test
a=ice-pwd:testpassword
a=ice-options:trickle
a=fingerprint:sha-256 00:00:00:00:00:00:00:00:00:00:00:00:00:00:00:00:00:00:00:00:00:00:00:00:00:00:00:00:00:00:00:00
a=setup:actpass
a=mid:0
a=extmap:1 urn:ietf:params:rtp-hdrext:ssrc-audio-level
a=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time
a=extmap:3 urn:3gpp:video-orientation
a=extmap:4 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01
a=extmap:5 http://www.webrtc.org/experiments/rtp-hdrext/playout-delay
a=extmap:6 http://www.webrtc.org/experiments/rtp-hdrext/video-content-type
a=extmap:7 http://www.webrtc.org/experiments/rtp-hdrext/video-timing
a=extmap:8 http://www.webrtc.org/experiments/rtp-hdrext/color-space
a=extmap:9 urn:ietf:params:rtp-hdrext:sdes:mid
a=extmap:10 urn:ietf:params:rtp-hdrext:sdes:rtp-stream-id
a=extmap:11 urn:ietf:params:rtp-hdrext:sdes:repaired-rtp-stream-id
a=recvonly
a=rtcp-mux
a=rtcp-rsize
a=rtpmap:96 VP8/90000
a=rtcp-fb:96 goog-remb
a=rtcp-fb:96 transport-cc
a=rtcp-fb:96 ccm fir
a=rtcp-fb:96 nack
a=rtcp-fb:96 nack pli
a=rtpmap:97 VP9/90000
a=rtcp-fb:97 goog-remb
a=rtcp-fb:97 transport-cc
a=rtcp-fb:97 ccm fir
a=rtcp-fb:97 nack
a=rtcp-fb:97 nack pli
a=rtpmap:98 H264/90000
a=rtcp-fb:98 goog-remb
a=rtcp-fb:98 transport-cc
a=rtcp-fb:98 ccm fir
a=rtcp-fb:98 nack
a=rtcp-fb:98 nack pli
a=fmtp:98 level-asymmetry-allowed=1;packetization-mode=1;profile-level-id=42e01f
a=rtpmap:99 H264/90000
a=rtcp-fb:99 goog-remb
a=rtcp-fb:99 transport-cc
a=rtcp-fb:99 ccm fir
a=rtcp-fb:99 nack
a=rtcp-fb:99 nack pli
a=fmtp:99 level-asymmetry-allowed=1;packetization-mode=1;profile-level-id=42001f
m=audio 9 UDP/TLS/RTP/SAVPF 111 103 104
c=IN IP4 0.0.0.0
a=rtcp:9 IN IP4 0.0.0.0
a=ice-ufrag:test
a=ice-pwd:testpassword
a=ice-options:trickle
a=fingerprint:sha-256 00:00:00:00:00:00:00:00:00:00:00:00:00:00:00:00:00:00:00:00:00:00:00:00:00:00:00:00:00:00:00:00
a=setup:actpass
a=mid:1
a=extmap:14 urn:ietf:params:rtp-hdrext:ssrc-audio-level
a=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time
a=extmap:4 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01
a=extmap:9 urn:ietf:params:rtp-hdrext:sdes:mid
a=extmap:10 urn:ietf:params:rtp-hdrext:sdes:rtp-stream-id
a=extmap:11 urn:ietf:params:rtp-hdrext:sdes:repaired-rtp-stream-id
a=recvonly
a=rtcp-mux
a=rtpmap:111 opus/48000/2
a=rtcp-fb:111 transport-cc
a=fmtp:111 minptime=10;useinbandfec=1
a=rtpmap:103 ISAC/16000
a=rtpmap:104 ISAC/32000
`;
}

function analyzeSDPAnswer(sdp) {
  console.log('\n🔍 Analyzing MediaMTX SDP Answer...');
  
  const lines = sdp.split('\n');
  const codecs = [];
  let currentMedia = null;
  
  lines.forEach(line => {
    line = line.trim();
    
    if (line.startsWith('m=video')) {
      currentMedia = 'video';
      console.log('📹 Video media section found');
    } else if (line.startsWith('m=audio')) {
      currentMedia = 'audio';
      console.log('🔊 Audio media section found');
    } else if (line.startsWith('a=rtpmap:')) {
      const match = line.match(/a=rtpmap:(\d+)\s+([^\/]+)\/(\d+)/);
      if (match) {
        const [, payloadType, codec, clockRate] = match;
        codecs.push({
          media: currentMedia,
          payloadType: parseInt(payloadType),
          codec,
          clockRate: parseInt(clockRate)
        });
        console.log(`  📦 Codec: ${codec}/${clockRate} (PT: ${payloadType})`);
      }
    } else if (line.startsWith('a=fmtp:')) {
      const match = line.match(/a=fmtp:(\d+)\s+(.+)/);
      if (match) {
        const [, payloadType, params] = match;
        console.log(`  ⚙️  Format params for PT ${payloadType}: ${params}`);
        
        // Check H.264 profile
        if (params.includes('profile-level-id=')) {
          const profileMatch = params.match(/profile-level-id=([a-fA-F0-9]{6})/);
          if (profileMatch) {
            const profile = profileMatch[1];
            const isSupported = BROWSER_H264_PROFILES.includes(profile.toLowerCase());
            console.log(`    🧬 H.264 Profile: ${profile} ${isSupported ? '✅ Supported' : '❌ May not be supported'}`);
          }
        }
      }
    }
  });
  
  // Check codec compatibility
  console.log('\n🔄 Codec Compatibility Analysis:');
  codecs.forEach(codec => {
    const browserCodecs = BROWSER_CODECS[codec.media] || [];
    const mimeType = `${codec.media}/${codec.codec}`;
    const isSupported = browserCodecs.includes(mimeType);
    console.log(`  ${isSupported ? '✅' : '❌'} ${mimeType} - ${isSupported ? 'Supported' : 'Not supported'} by browser`);
  });
}

function makeRequest(url, options = {}) {
  return new Promise((resolve, reject) => {
    const urlObj = new URL(url);
    const isHttps = urlObj.protocol === 'https:';
    const lib = isHttps ? https : http;
    
    const reqOptions = {
      hostname: urlObj.hostname,
      port: urlObj.port || (isHttps ? 443 : 80),
      path: urlObj.pathname + urlObj.search,
      method: options.method || 'GET',
      headers: options.headers || {}
    };
    
    const req = lib.request(reqOptions, (res) => {
      let data = '';
      res.on('data', chunk => data += chunk);
      res.on('end', () => {
        if (res.statusCode >= 200 && res.statusCode < 300) {
          resolve(data);
        } else {
          reject(new Error(`HTTP ${res.statusCode}: ${data}`));
        }
      });
    });
    
    req.on('error', reject);
    
    if (options.body) {
      req.write(options.body);
    }
    
    req.end();
  });
}

async function main() {
  console.log('🚀 Camera Streaming Codec Debug Tool\n');
  
  const mediamtxOk = await checkMediaMTXStatus();
  
  if (mediamtxOk) {
    await testWHEPOffer();
  }
  
  console.log('\n💡 Recommendations:');
  console.log('1. Check browser console for detailed WebRTC logs');
  console.log('2. Use the WebRTC Debug component in the viewer');
  console.log('3. Verify H.264 profile compatibility');
  console.log('4. Check if MediaMTX is transcoding properly');
  console.log('5. Monitor RTP packet flow in publisher logs');
}

if (require.main === module) {
  main().catch(console.error);
}
