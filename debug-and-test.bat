@echo off
echo ========================================
echo Camera Streaming Debug and Test Script
echo ========================================
echo.

echo 1. Building all components...
call build-all.bat
if %ERRORLEVEL% neq 0 (
    echo ❌ Build failed!
    pause
    exit /b 1
)

echo.
echo 2. Running codec compatibility check...
node debug-codec-mismatch.js

echo.
echo 3. Starting components for testing...
echo.

echo Starting MediaMTX...
start "MediaMTX" cmd /k "cd publisher && mediamtx.exe"
timeout /t 3

echo Starting Server...
start "Server" cmd /k "cd server && server.exe"
timeout /t 2

echo Starting Publisher...
start "Publisher" cmd /k "cd publisher && publisher.exe"
timeout /t 2

echo Starting Viewer...
start "Viewer" cmd /k "cd viewer && npm run start"

echo.
echo ========================================
echo All components started!
echo ========================================
echo.
echo 📋 Testing Checklist:
echo.
echo 1. Open browser to http://localhost:3000
echo 2. Click "Show Debug" button in bottom-right
echo 3. Select stream and monitor debug info
echo 4. Check browser console for detailed logs
echo 5. Look for these potential issues:
echo    - Codec mismatch warnings
echo    - H.264 profile incompatibility
echo    - Video element readyState stuck at 0-1
echo    - Zero video dimensions
echo    - RTP packet flow issues
echo.
echo 🔧 Debug Commands:
echo    - Check MediaMTX: curl http://localhost:8889/v3/paths/list
echo    - Check Server: curl http://localhost:8080/health
echo    - View logs in each terminal window
echo.
echo Press any key to continue monitoring...
pause > nul

echo.
echo 📊 Monitoring mode - Press Ctrl+C to stop all services
echo.

:monitor_loop
timeout /t 10 > nul
echo [%time%] Services running... (Ctrl+C to stop)
goto monitor_loop
