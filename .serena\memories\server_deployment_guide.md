# Server Deployment Guide

## Build Process
The server is a Go application that can be built for Linux using:
- `build.sh` - Linux build script
- `build.bat` - Windows build script  
- `Dockerfile` - For containerized deployment

## Binary Information
- **Executable Name**: `server`
- **Architecture**: ELF 64-bit LSB executable, x86-64
- **Build Type**: Statically linked with debug info
- **Size**: ~10MB
- **Location**: Same directory as source code

## Environment Variables
- **CAM_SERVER_PORT**: Port number for the server (default: 8612)
- Used in both manual execution and systemd service

## Deployment Options

### 1. Manual Execution
```bash
CAM_SERVER_PORT=8612 ./server
```

### 2. Systemd Service
- Automatic startup on boot
- Automatic restart on failure
- Proper logging via journald
- Managed via standard systemctl commands
- Service name: `webrtc-signaling`

### 3. Docker (Available but not configured)
- Dockerfile exists in server directory
- Not currently set up for the service deployment

## File Structure
```
server/
├── main.go           # Source code
├── go.mod           # Go module definition
├── go.sum           # Go module checksums
├── server           # Linux binary
├── server.exe       # Windows binary
├── build.sh         # Linux build script
├── build.bat        # Windows build script
├── install-service.sh  # Service installation
├── remove-service.sh   # Service removal
└── Dockerfile       # Container build file
```

## Security Considerations
- Server runs as regular user (tyris), not root
- Systemd security hardening was removed due to compatibility issues
- Binary is statically linked (good for deployment isolation)
- Service listens on port 8612 (ensure firewall rules if needed)