import express from 'express';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const app = express();
const port = process.env.CAM_VIEWER_PORT || 3000;

// Serve static files from build/client
app.use(express.static(path.join(__dirname, 'build/client')));

// Handle client-side routing - always serve index.html for non-asset requests
app.get('*', (req, res) => {
  res.sendFile(path.join(__dirname, 'build/client/index.html'));
});

app.listen(port, () => {
  console.log(`Viewer server running at http://localhost:${port}`);
});