import { useEffect, useRef, useState } from 'react';
import { useAtomValue, useSetAtom } from 'jotai';
import { <PERSON>Left, Loader2, WifiOff, AlertCircle } from 'lucide-react';
import { sendWebSocketMessageAtom, connectionStatusAtom } from '../store/websocket';
import type { Stream, ViewerOfferData, WebRTCAnswerData, ICECandidateData } from '../types/stream';
import { WebRTCDebug } from './webrtc-debug';

interface StreamViewerProps {
  stream: Stream;
  onBack: () => void;
}

export function StreamViewer({ stream, onBack }: StreamViewerProps) {
  const videoRef = useRef<HTMLVideoElement>(null);
  const peerConnectionRef = useRef<RTCPeerConnection | null>(null);
  const pendingCandidatesRef = useRef<ICECandidateData[]>([]);
  const sendMessage = useSetAtom(sendWebSocketMessageAtom);
  const connectionStatus = useAtomValue(connectionStatusAtom);
  const [status, setStatus] = useState<'connecting' | 'connected' | 'error'>('connecting');
  const [mediaStreamRef, setMediaStreamRef] = useState<MediaStream | null>(null);

  useEffect(() => {
    if (connectionStatus === 'connected') {
      initWebRTC();
    } else {
      setStatus('error');
    }

    // Listen for WebRTC answers
    const handleWebRTCAnswer = (event: CustomEvent<WebRTCAnswerData>) => {
      handleAnswer(event.detail.sdp);
    };

    // Listen for ICE candidates
    const handleICECandidate = (event: CustomEvent<ICECandidateData>) => {
      handleRemoteICECandidate(event.detail);
    };

    window.addEventListener('webrtc-answer', handleWebRTCAnswer as EventListener);
    window.addEventListener('ice-candidate', handleICECandidate as EventListener);

    return () => {
      cleanup();
      window.removeEventListener('webrtc-answer', handleWebRTCAnswer as EventListener);
      window.removeEventListener('ice-candidate', handleICECandidate as EventListener);
    };
  }, [stream, connectionStatus]);

  const initWebRTC = async () => {
    try {
      setStatus('connecting');
      console.log('Initializing WebRTC for stream:', stream.path);

      // Log browser codec capabilities
      console.log('=== BROWSER CODEC CAPABILITIES ===');
      const videoCapabilities = RTCRtpReceiver.getCapabilities('video');
      console.log('Video codecs supported:');
      videoCapabilities?.codecs.forEach(codec => {
        console.log(`- ${codec.mimeType} (clockRate: ${codec.clockRate})`);
        if (codec.mimeType.includes('H264') && codec.sdpFmtpLine) {
          console.log(`  H264 Profile: ${codec.sdpFmtpLine}`);
        }
      });

      const audioCapabilities = RTCRtpReceiver.getCapabilities('audio');
      console.log('Audio codecs supported:');
      audioCapabilities?.codecs.forEach(codec => {
        console.log(`- ${codec.mimeType} (clockRate: ${codec.clockRate})`);
      });

      // Create peer connection
      const pc = new RTCPeerConnection({
        iceServers: [
          { urls: ['stun:stun.cloudflare.com:3478', 'stun:stun.cloudflare.com:53'] },
          {
            urls: [
              'turn:turn.cloudflare.com:3478?transport=udp',
              'turn:turn.cloudflare.com:3478?transport=tcp',
              'turns:turn.cloudflare.com:5349?transport=tcp',
              'turn:turn.cloudflare.com:53?transport=udp',
              'turn:turn.cloudflare.com:80?transport=tcp',
              'turns:turn.cloudflare.com:443?transport=tcp',
            ],
            username: 'g08cb9a5a9fc9e7ef4b50fb14fb284037d28c3a97de1a9a4c4f2f2c9d2b0b128',
            credential: '356d8694f96eca8df8b150359d737bcd9619240fe646c1b998355945ce304c1d',
          },
        ],
      });
      (window as any).pc = pc

      peerConnectionRef.current = pc;

      // Add transceiver for receiving video
      const direction = 'recvonly';
      pc.addTransceiver('video', { direction });
      pc.addTransceiver('audio', { direction });

      // Handle ICE candidates (trickle ICE)
      pc.onicecandidate = (event) => {
        if (event.candidate) {
          console.log('Generated local ICE candidate:', event.candidate.candidate);

          // Send ICE candidate to server immediately
          const candidateData: ICECandidateData = {
            viewer_id: '', // Will be set by server
            candidate: event.candidate.candidate,
            sdp_mid: event.candidate.sdpMid || '',
            sdp_mline_index: event.candidate.sdpMLineIndex || 0,
          };

          sendMessage({
            type: 'viewer_ice_candidate',
            data: candidateData,
          });
        } else {
          console.log('ICE candidate gathering complete');
        }
      };

      // Handle incoming stream - Create a single MediaStream to collect all tracks
      let combinedStream: MediaStream | null = null;

      pc.ontrack = (event) => {
        console.log('=== TRACK EVENT DETAILS ===');
        console.log('Track kind:', event.track.kind);
        console.log('Track id:', event.track.id);
        console.log('Track label:', event.track.label);
        console.log('Track readyState:', event.track.readyState);
        console.log('Track enabled:', event.track.enabled);
        console.log('Track muted:', event.track.muted);
        console.log('Streams count:', event.streams?.length || 0);

        // Add track event listeners to monitor state changes
        event.track.addEventListener('mute', () => {
          console.log('Track MUTED:', event.track.kind, event.track.id);
        });

        event.track.addEventListener('unmute', () => {
          console.log('✅ Track UNMUTED:', event.track.kind, event.track.id);
          // Force video element to refresh when track unmutes
          if (event.track.kind === 'video' && videoRef.current) {
            console.log('🔄 Video track unmuted, refreshing video element...');
            const currentSrc = videoRef.current.srcObject;
            videoRef.current.srcObject = null;
            setTimeout(() => {
              if (videoRef.current) {
                videoRef.current.srcObject = currentSrc;
                videoRef.current.play().catch(e => console.error('Play after unmute failed:', e));
              }
            }, 100);
          }
        });

        event.track.addEventListener('ended', () => {
          console.log('Track ENDED:', event.track.kind, event.track.id);
        });

        if (event.streams?.[0]) {
          const stream = event.streams[0];
          console.log('Stream id:', stream.id);
          console.log('Stream active:', stream.active);
          console.log('Stream tracks:', stream.getTracks().length);
          stream.getTracks().forEach(track => {
            console.log(`- ${track.kind} track:`, track.id, 'ready:', track.readyState);
          });
        }

        // Create or reuse combined stream
        if (!combinedStream) {
          combinedStream = new MediaStream();
          setMediaStreamRef(combinedStream);
          
          // Set stream to video element immediately
          if (videoRef.current) {
            videoRef.current.srcObject = combinedStream;
            console.log('Video srcObject set to combined MediaStream');
          }
        }

        // Add track to combined stream
        combinedStream.addTrack(event.track);
        console.log(`Added ${event.track.kind} track to combined stream. Total tracks:`, combinedStream.getTracks().length);

        // 🔧 FIX: Handle muted track - this is critical for video decode
        if (event.track.muted) {
          console.warn(`⚠️ Track ${event.track.kind} is MUTED - this will prevent video decode!`);
          console.log('🔧 Implementing muted track fixes...');

          // Strategy 1: Force track enabled state
          event.track.enabled = false;
          setTimeout(() => {
            event.track.enabled = true;
            console.log('✅ Track re-enabled after mute detection');
          }, 100);

          // Strategy 2: Clone the track to potentially get unmuted version
          try {
            const clonedTrack = event.track.clone();
            if (!clonedTrack.muted) {
              console.log('✅ Successfully cloned unmuted track');
              combinedStream.removeTrack(event.track);
              combinedStream.addTrack(clonedTrack);
              console.log('✅ Replaced muted track with cloned track');
            } else {
              console.warn('⚠️ Cloned track is still muted');
            }
          } catch (e) {
            console.error('❌ Failed to clone track:', e);
          }
        }

        // Always ensure track is enabled
        event.track.enabled = true;
        console.log(`✅ Track ${event.track.kind} - enabled: ${event.track.enabled}, muted: ${event.track.muted}`);

        // Log current stream state
        console.log('Combined stream tracks:');
        combinedStream.getTracks().forEach(track => {
          console.log(`- ${track.kind} track:`, track.id, 'ready:', track.readyState, 'enabled:', track.enabled);
        });

        // Log video element state after adding track
        setTimeout(() => {
          if (videoRef.current) {
            console.log('=== VIDEO ELEMENT STATE ===');
            console.log('Video readyState:', videoRef.current.readyState);
            console.log('Video videoWidth:', videoRef.current.videoWidth);
            console.log('Video videoHeight:', videoRef.current.videoHeight);
            console.log('Video duration:', videoRef.current.duration);
            console.log('Video paused:', videoRef.current.paused);
            console.log('Video ended:', videoRef.current.ended);
            console.log('Video currentTime:', videoRef.current.currentTime);
            
            // Check srcObject tracks
            const srcObject = videoRef.current.srcObject as MediaStream;
            if (srcObject) {
              console.log('srcObject tracks:');
              srcObject.getTracks().forEach(track => {
                console.log(`- ${track.kind}: ${track.id}, enabled: ${track.enabled}, readyState: ${track.readyState}`);
              });
            }
          }
        }, 1000);

        // Set status to connected when we have both video and audio tracks, or at least one track
        const totalTracks = combinedStream.getTracks().length;
        const videoTracks = combinedStream.getVideoTracks().length;
        const audioTracks = combinedStream.getAudioTracks().length;
        
        console.log(`Stream status: ${totalTracks} total tracks (${videoTracks} video, ${audioTracks} audio)`);
        
        if (totalTracks > 0) {
          setStatus('connected');

          // 🔧 Monitor track states periodically and auto-fix
          const monitorInterval = setInterval(() => {
            if (combinedStream) {
              const tracks = combinedStream.getTracks();
              tracks.forEach((track, index) => {
                if (track.kind === 'video' && track.muted) {
                  console.warn(`⚠️ Video track ${index} is still muted - AUTO-FIXING...`);

                  // Auto-fix: Toggle enabled state
                  track.enabled = false;
                  setTimeout(() => {
                    track.enabled = true;
                    console.log(`🔧 Auto-fixed: Re-enabled muted track ${index}`);
                  }, 100);

                  // Auto-fix: Try cloning if still muted
                  setTimeout(() => {
                    if (track.muted && combinedStream) {
                      try {
                        const clonedTrack = track.clone();
                        if (!clonedTrack.muted) {
                          combinedStream.removeTrack(track);
                          combinedStream.addTrack(clonedTrack);
                          console.log(`🔧 Auto-fixed: Replaced muted track ${index} with clone`);
                        }
                      } catch (e) {
                        console.error(`❌ Auto-fix clone failed for track ${index}:`, e);
                      }
                    }
                  }, 200);
                }
              });

              // Check video element state
              if (videoRef.current) {
                const video = videoRef.current;
                if (video.readyState === 0 && tracks.length > 0) {
                  console.warn('⚠️ Video element has tracks but readyState is still 0 - AUTO-FIXING...');
                  video.load();
                  setTimeout(() => {
                    if (video.paused) {
                      video.play().catch(e => console.error('Auto-play failed:', e));
                    }
                  }, 500);
                }
              }
            }
          }, 3000); // Check every 3 seconds

          // Clear interval after 30 seconds
          setTimeout(() => clearInterval(monitorInterval), 30000);
        }
      };

      pc.oniceconnectionstatechange = () => {
        console.log('ICE connection state:', pc.iceConnectionState);
        if (pc.iceConnectionState === 'failed' || pc.iceConnectionState === 'disconnected') {
          setStatus('error');
        }
      };

      // Create offer
      const offer = await pc.createOffer();
      await pc.setLocalDescription(offer);

      // Send offer to server immediately (trickle ICE will handle candidates separately)
      const offerData: ViewerOfferData = {
        stream_path: stream.path,
        sdp: offer.sdp!, // Use offer SDP directly, not waiting for ICE gathering
        viewer_id: '', // Will be set by server
      };

      console.log('Sending offer immediately with trickle ICE support');
      sendMessage({
        type: 'viewer_offer',
        data: offerData,
      });
    } catch (error) {
      console.error('Error initializing WebRTC:', error);
      setStatus('error');
    }
  };

  const handleAnswer = async (answerSDP: string) => {
    try {
      console.log('Received WebRTC answer');
      const pc = peerConnectionRef.current;
      if (!pc) return;

      const answer = new RTCSessionDescription({
        type: 'answer',
        sdp: answerSDP,
      });

      await pc.setRemoteDescription(answer);
      console.log('WebRTC answer set successfully');

      // Debug transceivers after answer
      console.log('=== TRANSCEIVER STATES ===');
      pc.getTransceivers().forEach((transceiver, index) => {
        console.log(`Transceiver ${index}:`, {
          direction: transceiver.direction,
          currentDirection: transceiver.currentDirection,
          mid: transceiver.mid,
          kind: transceiver.receiver.track?.kind,
          sender: transceiver.sender.track !== null,
          receiver: transceiver.receiver.track !== null
        });

        // Log codec info
        if (transceiver.receiver) {
          const params = transceiver.receiver.getParameters();
          console.log(`  Receiver parameters:`, params);
          params.codecs?.forEach(codec => {
            console.log(`  Codec: ${codec.mimeType}, pt: ${codec.payloadType}`);
            if (codec.mimeType.includes('H264') && codec.sdpFmtpLine) {
              console.log(`    H264 Profile: ${codec.sdpFmtpLine}`);
            }
          });
        }
      });

      // Log SDP details for codec info
      console.log('=== ANSWER SDP CODECS ===');
      const sdpLines = answerSDP.split('\n');
      sdpLines.forEach(line => {
        if (line.startsWith('a=rtpmap:') || line.startsWith('a=fmtp:')) {
          console.log('SDP:', line);
        }
      });

      // Process buffered ICE candidates
      const pendingCandidates = pendingCandidatesRef.current;
      if (pendingCandidates.length > 0) {
        console.log(`Processing ${pendingCandidates.length} buffered ICE candidates`);

        for (const candidateData of pendingCandidates) {
          try {
            const candidate = new RTCIceCandidate({
              candidate: candidateData.candidate,
              sdpMid: candidateData.sdp_mid,
              sdpMLineIndex: candidateData.sdp_mline_index,
            });

            await pc.addIceCandidate(candidate);
            console.log('Buffered ICE candidate added successfully:', candidateData.candidate);
          } catch (candidateError) {
            console.error('Error adding buffered ICE candidate:', candidateError);
          }
        }

        // Clear the buffer
        pendingCandidatesRef.current = [];
      }
    } catch (error) {
      console.error('Error handling WebRTC answer:', error);
      setStatus('error');
    }
  };

  const handleRemoteICECandidate = async (candidateData: ICECandidateData) => {
    try {
      const pc = peerConnectionRef.current;
      if (!pc) {
        console.warn('Peer connection not available for ICE candidate');
        return;
      }

      console.log('Adding remote ICE candidate:', candidateData.candidate);

      // Check if remote description is set
      if (!pc.remoteDescription) {
        console.log('Remote description not set yet, buffering ICE candidate');
        pendingCandidatesRef.current.push(candidateData);
        return;
      }

      const candidate = new RTCIceCandidate({
        candidate: candidateData.candidate,
        sdpMid: candidateData.sdp_mid,
        sdpMLineIndex: candidateData.sdp_mline_index,
      });

      await pc.addIceCandidate(candidate);
      console.log('Remote ICE candidate added successfully');
    } catch (error) {
      console.error('Error adding remote ICE candidate:', error);
    }
  };

  const cleanup = () => {
    if (peerConnectionRef.current) {
      peerConnectionRef.current.close();
      peerConnectionRef.current = null;
    }
    if (videoRef.current) {
      videoRef.current.srcObject = null;
    }
    // Clear pending ICE candidates
    pendingCandidatesRef.current = [];
    setMediaStreamRef(null);
  };

  const retry = () => {
    cleanup();
    initWebRTC();
  };

  return (
    <div className="w-full h-screen flex flex-col bg-black">
      {/* Header */}
      <div className="bg-gray-900 text-white p-4 flex items-center gap-4">
        <button
          onClick={onBack}
          className="flex items-center gap-2 px-3 py-2 bg-gray-700 rounded-lg hover:bg-gray-600 transition-colors"
        >
          <ArrowLeft className="h-4 w-4" />
          Back
        </button>
        <div>
          <h1 className="text-lg font-semibold">{stream.name}</h1>
          <p className="text-sm text-gray-400">Path: {stream.path}</p>
        </div>
        <div className="ml-auto flex items-center gap-2">
          <div
            className={`w-2 h-2 rounded-full ${
              status === 'connected' ? 'bg-green-500' : status === 'connecting' ? 'bg-yellow-500' : 'bg-red-500'
            }`}
          />
          <span className="text-sm capitalize">{status}</span>
        </div>
      </div>

      {/* Video Container */}
      <div className="flex-1 flex items-center justify-center relative">
        {status === 'connecting' && (
          <div className="flex flex-col items-center gap-4 text-white">
            <Loader2 className="h-8 w-8 animate-spin" />
            <p>Connecting to stream...</p>
          </div>
        )}

        {status === 'error' && (
          <div className="flex flex-col items-center gap-4 text-white">
            <AlertCircle className="h-12 w-12 text-red-500" />
            <p>Connection failed</p>
            <button
              onClick={retry}
              className="px-4 py-2 bg-red-600 rounded-lg hover:bg-red-700 transition-colors"
            >
              Retry
            </button>
          </div>
        )}

        <video
          ref={videoRef}
          autoPlay
          playsInline
          muted={false}  // 🔧 Don't mute video element - this can cause track muting
          className="w-full h-full object-contain"
          onLoadedMetadata={() => {
            console.log('✅ Video metadata loaded');
            if (videoRef.current) {
              console.log('Video dimensions:', videoRef.current.videoWidth, 'x', videoRef.current.videoHeight);
              // Force play if paused
              if (videoRef.current.paused) {
                console.log('🎬 Video is paused, attempting to play...');
                videoRef.current.play().catch(e => console.error('Play failed:', e));
              }
            }
          }}
          onCanPlay={() => {
            console.log('Video can play');
          }}
          onError={(e) => {
            console.error('Video error:', e);
            const video = e.target as HTMLVideoElement;
            console.error('Video error details:', {
              error: video?.error,
              networkState: video?.networkState,
              readyState: video?.readyState,
              currentSrc: video?.currentSrc
            });
          }}
          onStalled={() => {
            console.warn('Video stalled - buffering issues detected');
          }}
          onSuspend={() => {
            console.warn('Video suspended - loading paused');
          }}
          onWaiting={() => {
            console.warn('Video waiting - buffering more data');
          }}
          onProgress={() => {
            if (videoRef.current) {
              const buffered = videoRef.current.buffered;
              if (buffered.length > 0) {
                console.log('Video buffered:', buffered.start(0), 'to', buffered.end(buffered.length - 1));
              }
            }
          }}
        />

        {/* Debug Info */}
        {status === 'connected' && (
          <div className="absolute top-4 right-4 bg-black bg-opacity-50 text-white p-3 rounded-lg text-sm font-mono">
            <div>Status: {status}</div>
            <div>Stream: {mediaStreamRef ? `${mediaStreamRef.getTracks().length} tracks` : 'No stream'}</div>
            {mediaStreamRef && (
              <>
                <div>Video: {mediaStreamRef.getVideoTracks().length}</div>
                <div>Audio: {mediaStreamRef.getAudioTracks().length}</div>
              </>
            )}
            <button
              onClick={() => {
                if (videoRef.current) {
                  const stream = videoRef.current.srcObject as MediaStream;
                  if (stream) {
                    console.log('=== CURRENT STREAM DEBUG ===');
                    console.log('Stream active:', stream.active);
                    console.log('Total tracks:', stream.getTracks().length);
                    stream.getTracks().forEach(track => {
                      console.log(`- ${track.kind}: ${track.id}, enabled: ${track.enabled}, readyState: ${track.readyState}, muted: ${track.muted}`);
                    });
                  }
                }
              }}
              className="mt-2 px-2 py-1 bg-gray-600 rounded text-xs hover:bg-gray-500"
            >
              Debug Stream
            </button>
            <button
              onClick={() => {
                if (videoRef.current) {
                  const stream = videoRef.current.srcObject as MediaStream;
                  if (stream) {
                    console.log('🔧 AGGRESSIVE MUTED VIDEO FIX STARTING...');
                    const videoTracks = stream.getVideoTracks();

                    videoTracks.forEach((track, index) => {
                      console.log(`Track ${index}: muted=${track.muted}, enabled=${track.enabled}, readyState=${track.readyState}`);

                      if (track.muted) {
                        console.log(`🚨 Found muted track ${index}, applying ALL fixes...`);

                        // Fix 1: Toggle enabled state
                        track.enabled = false;
                        setTimeout(() => {
                          track.enabled = true;
                          console.log(`✅ Fix 1: Re-enabled track ${index}`);
                        }, 50);

                        // Fix 2: Try to clone and replace
                        try {
                          const clonedTrack = track.clone();
                          if (!clonedTrack.muted) {
                            stream.removeTrack(track);
                            stream.addTrack(clonedTrack);
                            console.log(`✅ Fix 2: Replaced with cloned track ${index}`);
                          }
                        } catch (e) {
                          console.error(`❌ Fix 2 failed for track ${index}:`, e);
                        }

                        // Fix 3: Create new MediaStream
                        setTimeout(() => {
                          const newStream = new MediaStream();
                          stream.getTracks().forEach(t => {
                            if (!t.muted) newStream.addTrack(t);
                          });
                          if (newStream.getTracks().length > 0) {
                            videoRef.current!.srcObject = newStream;
                            console.log(`✅ Fix 3: Created new stream with ${newStream.getTracks().length} tracks`);
                          }
                        }, 200);
                      }
                    });

                    // Fix 4: Force video element operations
                    setTimeout(() => {
                      if (videoRef.current) {
                        console.log('🔧 Fix 4: Forcing video element reload and play...');
                        videoRef.current.load();
                        setTimeout(() => {
                          if (videoRef.current) {
                            videoRef.current.play().catch(e => console.error('Play failed:', e));
                          }
                        }, 300);
                      }
                    }, 400);

                    console.log('🔧 All muted video fixes applied!');
                  }
                }
              }}
              className="mt-1 px-2 py-1 bg-red-600 rounded text-xs hover:bg-red-500"
            >
              🔧 Fix Muted Video
            </button>
          </div>
        )}
      </div>

      {/* WebRTC Debug Component */}
      <WebRTCDebug
        peerConnection={peerConnectionRef.current}
        videoElement={videoRef.current}
      />
    </div>
  );
}
