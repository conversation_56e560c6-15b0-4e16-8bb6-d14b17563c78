#!/bin/bash
echo "Building server..."
go mod tidy

if [ -z "$1" ]; then
    echo "Building for current platform..."
    go build -o server .
    echo "Server built successfully as server"
elif [ "$1" = "linux" ]; then
    echo "Building for Linux..."
    GOOS=linux GOARCH=amd64 go build -o server .
    echo "Server built successfully as server (Linux)"
elif [ "$1" = "windows" ]; then
    echo "Building for Windows..."
    GOOS=windows GOARCH=amd64 go build -o server.exe .
    echo "Server built successfully as server.exe (Windows)"
elif [ "$1" = "mac" ]; then
    echo "Building for macOS..."
    GOOS=darwin GOARCH=amd64 go build -o server .
    echo "Server built successfully as server (macOS)"
elif [ "$1" = "arm64" ]; then
    echo "Building for Linux ARM64..."
    GOOS=linux GOARCH=arm64 go build -o server .
    echo "Server built successfully as server (Linux ARM64)"
else
    echo "Usage: ./build.sh [linux|windows|mac|arm64]"
    echo "  No argument: Build for current platform"
    echo "  linux:       Build for Linux x64"
    echo "  windows:     Build for Windows x64"
    echo "  mac:         Build for macOS x64"
    echo "  arm64:       Build for Linux ARM64"
fi