@echo off
echo 🔍 Testing Old vs New Code to Find Root Cause...

echo.
echo 📋 ANALYSIS: Code differences found:
echo.
echo OLD CODE (WORKING):
echo ✅ Direct WHEP API call to MediaMTX
echo ✅ Simple WebSocket relay: Viewer → Server → Publisher → MediaMTX
echo ✅ No WebRTC proxy layer
echo ✅ No manual track forwarding
echo ✅ MediaMTX handles all WebRTC complexity
echo.
echo NEW CODE (MUTED VIDEO):
echo ❌ Complex dual peer connection architecture
echo ❌ Manual RTP packet forwarding
echo ❌ WebRTC proxy layer between viewer and MediaMTX
echo ❌ Trickle ICE implementation
echo ❌ Track replacement and payload type mapping
echo.
echo 🎯 ROOT CAUSE HYPOTHESIS:
echo The WebRTC proxy layer is causing video tracks to be muted
echo because of improper track handling during forwarding.
echo.
echo 🔧 POTENTIAL FIXES:
echo.
echo Option 1: Revert to old simple approach
echo   - Remove WebRTC proxy layer
echo   - Use direct WHEP API calls
echo   - Let MediaMTX handle WebRTC complexity
echo.
echo Option 2: Fix track forwarding in new code
echo   - Fix track muting during replacement
echo   - Ensure proper MediaStream handling
echo   - Fix RTP packet forwarding issues
echo.
echo Option 3: Hybrid approach
echo   - Keep trickle ICE benefits
echo   - Simplify track forwarding
echo   - Remove unnecessary complexity
echo.
echo 📊 RECOMMENDATION:
echo Start with Option 1 (revert to simple approach) to confirm
echo the root cause, then implement Option 3 for best of both worlds.
echo.
echo Would you like to:
echo [1] Build and test old publisher code
echo [2] Create simplified version of new code
echo [3] Analyze track forwarding issues in detail
echo.
set /p choice="Enter choice (1-3): "

if "%choice%"=="1" (
    echo.
    echo 🔨 Building old publisher code...
    cd publisher
    copy publisher_origin.go.bak publisher_old.go
    go build -o publisher_old.exe publisher_old.go
    if %ERRORLEVEL% equ 0 (
        echo ✅ Old publisher built successfully
        echo.
        echo 🚀 To test:
        echo 1. Stop current publisher
        echo 2. Run: cd publisher && publisher_old.exe
        echo 3. Test viewer - video should work without muting
    ) else (
        echo ❌ Old publisher build failed
    )
    cd ..
)

if "%choice%"=="2" (
    echo.
    echo 🔨 Creating simplified new code...
    echo This will create a hybrid version with:
    echo - Trickle ICE benefits
    echo - Direct WHEP API calls
    echo - No complex track forwarding
    echo.
    echo [Implementation needed - would you like me to create this?]
)

if "%choice%"=="3" (
    echo.
    echo 🔍 Analyzing track forwarding issues...
    echo.
    echo KEY DIFFERENCES IN TRACK HANDLING:
    echo.
    echo OLD: MediaMTX → Browser (direct WebRTC)
    echo NEW: MediaMTX → Publisher → Browser (forwarded WebRTC)
    echo.
    echo MUTING POINTS IN NEW CODE:
    echo 1. Track replacement in transceiver
    echo 2. RTP packet forwarding loop
    echo 3. MediaStream creation and management
    echo 4. Payload type translation
    echo.
    echo The track becomes muted during the forwarding process,
    echo likely due to improper MediaStream or track handling.
)

echo.
pause
