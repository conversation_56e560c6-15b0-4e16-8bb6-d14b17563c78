# Task Completion Checklist

## When Completing Development Tasks

### Frontend (Viewer) Tasks
1. **Code Quality Checks**
   ```bash
   cd viewer
   npm run typecheck        # Ensure TypeScript types are correct
   npm run format          # Format code with Prettier
   ```

2. **Build Verification**
   ```bash
   npm run build           # Verify production build works
   ```

3. **Development Testing**
   ```bash
   npm run dev             # Test in development mode
   ```

### Backend (Server/Publisher) Tasks
1. **Dependency Management**
   ```bash
   go mod tidy             # Clean and verify dependencies
   ```

2. **Build Verification**
   ```bash
   build.bat               # Ensure it builds without errors
   ```

3. **Cross-platform Testing** (if applicable)
   ```bash
   build.bat linux         # Test Linux build
   build.bat mac           # Test macOS build
   ```

### Integration Testing
1. **Component Startup Order**
   - Start MediaMTX server first
   - Start Server component
   - Start Publisher component
   - Start Viewer component

2. **Connection Verification**
   - Check WebSocket connections in browser console
   - Verify stream list appears in Viewer
   - Test stream selection functionality

### Code Review Checklist
- [ ] TypeScript types are properly defined
- [ ] Prettier formatting applied
- [ ] No console.error or unhandled errors
- [ ] WebSocket error handling implemented
- [ ] Cross-platform build compatibility
- [ ] Environment variables documented

### Documentation Updates
- [ ] Update README.md if new features added
- [ ] Update CLAUDE.md if development process changed
- [ ] Add comments for complex WebRTC logic

## Common Issues to Check
- **WebSocket connection errors**: Check server URL configuration
- **CORS issues**: Verify server CORS settings
- **Build failures**: Check Go version and dependencies
- **TypeScript errors**: Run `npm run typecheck` before committing