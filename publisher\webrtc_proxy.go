package main

import (
	"fmt"
	"io"
	"net/http"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/pion/webrtc/v3"
	"go.uber.org/zap"
)

// WebRTCProxy handles trickle ICE between viewer and MediaMTX
type WebRTCProxy struct {
	logger             *zap.Logger
	viewerConnection   *webrtc.PeerConnection
	mediaConnection    *webrtc.PeerConnection
	viewerAnswer       *webrtc.SessionDescription
	iceCandidateBuffer []webrtc.ICECandidate
	isMediaConnReady   bool
	mutex              sync.RWMutex
	onICECandidate     func(candidate webrtc.ICECandidate)
	onAnswer           func(sdp string)
	onError            func(error)
	ssrcMapping        map[uint32]uint32 // MediaMTX SSRC -> Viewer SSRC
}

// CodecPreferences stores viewer's preferred codecs
type CodecPreferences struct {
	videoCodec  string // "VP8", "VP9", "H264", "AV1"
	audioCodec  string // "opus", "PCMU", "PCMA"
	h264Profile string // if video is H264, store profile like "42e01f"
}

// ICE Server configurations
var ViewerICEServers = []webrtc.ICEServer{
	{
		URLs: []string{
			"stun:stun.cloudflare.com:3478",
			"stun:stun.cloudflare.com:53",
		},
	},
	{
		URLs: []string{
			"turn:turn.cloudflare.com:3478?transport=udp",
			"turn:turn.cloudflare.com:3478?transport=tcp",
			"turns:turn.cloudflare.com:5349?transport=tcp",
			"turn:turn.cloudflare.com:53?transport=udp",
			"turn:turn.cloudflare.com:80?transport=tcp",
			"turns:turn.cloudflare.com:443?transport=tcp",
		},
		Username:   "g08cb9a5a9fc9e7ef4b50fb14fb284037d28c3a97de1a9a4c4f2f2c9d2b0b128",
		Credential: "356d8694f96eca8df8b150359d737bcd9619240fe646c1b998355945ce304c1d",
	},
}

var MediaMTXICEServers = []webrtc.ICEServer{
	// For local MediaMTX connection, use Google STUN as fallback
	{
		URLs: []string{"stun:stun.l.google.com:19302"},
	},
}

// NewWebRTCProxy creates a new WebRTC proxy instance
func NewWebRTCProxy(logger *zap.Logger) *WebRTCProxy {
	return &WebRTCProxy{
		logger:             logger,
		iceCandidateBuffer: make([]webrtc.ICECandidate, 0),
		isMediaConnReady:   false,
		ssrcMapping:        make(map[uint32]uint32),
	}
}

// SetCallbacks sets the callback functions for ICE candidates, answer, and errors
func (w *WebRTCProxy) SetCallbacks(
	onICECandidate func(candidate webrtc.ICECandidate),
	onAnswer func(sdp string),
	onError func(error),
) {
	w.onICECandidate = onICECandidate
	w.onAnswer = onAnswer
	w.onError = onError
}

// parseViewerCodecPreferences extracts codec preferences from viewer SDP
func (w *WebRTCProxy) parseViewerCodecPreferences(sdp string) CodecPreferences {
	prefs := CodecPreferences{
		videoCodec:  "H264",   // Default fallback
		audioCodec:  "PCMU",   // Default fallback
		h264Profile: "42e01f", // Default profile
	}

	lines := strings.Split(sdp, "\n")

	// First, scan for all available video codecs to understand what's available
	availableCodecs := make([]string, 0)
	for _, line := range lines {
		line = strings.TrimSpace(line)
		if strings.HasPrefix(line, "a=rtpmap:") {
			if strings.Contains(line, "H264/90000") {
				availableCodecs = append(availableCodecs, "H264")
			} else if strings.Contains(line, "VP8/90000") {
				availableCodecs = append(availableCodecs, "VP8")
			} else if strings.Contains(line, "VP9/90000") {
				availableCodecs = append(availableCodecs, "VP9")
			} else if strings.Contains(line, "AV1/90000") {
				availableCodecs = append(availableCodecs, "AV1")
			}
		}
	}

	w.logger.Info("Available video codecs in viewer offer", zap.Strings("codecs", availableCodecs))

	// Now prioritize H264 if available, otherwise fallback to others
	for _, line := range lines {
		line = strings.TrimSpace(line)
		if strings.HasPrefix(line, "a=rtpmap:") {
			// Priority 1: H264
			if strings.Contains(line, "H264/90000") {
				prefs.videoCodec = "H264"
				w.logger.Info("Selected H264 codec (prioritized over VP8/VP9)")
				// Try to find profile in corresponding fmtp line
				parts := strings.Split(line, ":")
				if len(parts) >= 2 {
					payloadType := strings.Split(parts[1], " ")[0]
					// Look for matching fmtp line with profile
					for _, fmtpLine := range lines {
						if strings.HasPrefix(fmtpLine, "a=fmtp:"+payloadType) && strings.Contains(fmtpLine, "profile-level-id=") {
							profile := w.extractProfileFromFmtp(fmtpLine)
							if profile != "" {
								prefs.h264Profile = profile
								w.logger.Info("Found H264 profile in viewer offer", zap.String("profile", profile))
							}
							break
						}
					}
				}
				break
			}
		}
	}

	// If H264 not found, fallback to VP8/VP9/AV1 in order
	if prefs.videoCodec == "H264" { // H264 was selected above
		// Already selected H264, no fallback needed
	} else {
		for _, line := range lines {
			line = strings.TrimSpace(line)
			if strings.HasPrefix(line, "a=rtpmap:") {
				if strings.Contains(line, "VP8/90000") {
					prefs.videoCodec = "VP8"
					w.logger.Info("Selected VP8 codec (H264 not available)")
					break
				} else if strings.Contains(line, "VP9/90000") {
					prefs.videoCodec = "VP9"
					w.logger.Info("Selected VP9 codec (H264 and VP8 not available)")
					break
				} else if strings.Contains(line, "AV1/90000") {
					prefs.videoCodec = "AV1"
					w.logger.Info("Selected AV1 codec (H264/VP8/VP9 not available)")
					break
				}
			}
		}
	}

	// Look for audio codec preference
	for _, line := range lines {
		line = strings.TrimSpace(line)
		if strings.HasPrefix(line, "a=rtpmap:") {
			if strings.Contains(line, "opus/48000") {
				prefs.audioCodec = "opus"
				w.logger.Info("Found Opus codec in viewer offer")
				break
			} else if strings.Contains(line, "PCMU/8000") {
				prefs.audioCodec = "PCMU"
				w.logger.Info("Found PCMU codec in viewer offer")
				break
			} else if strings.Contains(line, "PCMA/8000") {
				prefs.audioCodec = "PCMA"
				w.logger.Info("Found PCMA codec in viewer offer")
				break
			}
		}
	}

	return prefs
}

// extractProfileFromFmtp extracts profile-level-id from fmtp line
func (w *WebRTCProxy) extractProfileFromFmtp(fmtpLine string) string {
	parts := strings.Split(fmtpLine, ";")
	for _, part := range parts {
		part = strings.TrimSpace(part)
		if strings.HasPrefix(part, "profile-level-id=") {
			return strings.TrimPrefix(part, "profile-level-id=")
		}
	}
	return ""
}

// HandleViewerOffer processes the initial offer from viewer and starts trickle ICE
func (w *WebRTCProxy) HandleViewerOffer(offer webrtc.SessionDescription, streamPath string) error {
	w.logger.Info("Creating viewer WebRTC connection for trickle ICE",
		zap.String("stream_path", streamPath),
		zap.Int("ice_servers_count", len(ViewerICEServers)),
		zap.Bool("has_turn_servers", true))

	// Create WebRTC configuration for viewer connection (with full STUN/TURN servers for trickle ICE)
	config := webrtc.Configuration{
		ICEServers: ViewerICEServers,
	}

	// Create peer connection for viewer
	viewerConn, err := webrtc.NewPeerConnection(config)
	if err != nil {
		return fmt.Errorf("failed to create viewer peer connection: %w", err)
	}
	w.viewerConnection = viewerConn

	// Set up ICE candidate handler for viewer connection (trickle ICE)
	viewerConn.OnICECandidate(func(candidate *webrtc.ICECandidate) {
		if candidate != nil {
			// Rate limit ICE candidate sending to prevent WebSocket race
			if w.onICECandidate != nil {
				// Capture callback to prevent nil pointer in goroutine
				candidateCallback := w.onICECandidate
				candidateValue := *candidate
				go func() {
					time.Sleep(10 * time.Millisecond) // Small delay between candidates
					if candidateCallback != nil {
						candidateCallback(candidateValue)
					}
				}()
			}
		} else {
			w.logger.Info("ICE candidate gathering complete for viewer")
		}
	})

	// Set up connection state change handler
	viewerConn.OnConnectionStateChange(func(state webrtc.PeerConnectionState) {
		w.logger.Info("Viewer connection state changed", zap.String("state", state.String()))
		if state == webrtc.PeerConnectionStateFailed {
			// Capture error callback to prevent nil pointer
			errorCallback := w.onError
			if errorCallback != nil {
				errorCallback(fmt.Errorf("viewer connection failed"))
			}
		}
	})

	// Set up ICE connection state handler for more detailed debugging
	viewerConn.OnICEConnectionStateChange(func(state webrtc.ICEConnectionState) {
		// Only log important ICE states
		if state == webrtc.ICEConnectionStateConnected || state == webrtc.ICEConnectionStateFailed {
			w.logger.Info("Viewer ICE connection state changed", zap.String("state", state.String()))
		}
	})

	// Add transceivers for sending to viewer (we'll add tracks later when MediaMTX connects)
	_, err = viewerConn.AddTransceiverFromKind(webrtc.RTPCodecTypeVideo, webrtc.RTPTransceiverInit{
		Direction: webrtc.RTPTransceiverDirectionSendonly,
	})
	if err != nil {
		w.logger.Error("Failed to add video transceiver for viewer", zap.Error(err))
		return fmt.Errorf("failed to add video transceiver: %w", err)
	}

	_, err = viewerConn.AddTransceiverFromKind(webrtc.RTPCodecTypeAudio, webrtc.RTPTransceiverInit{
		Direction: webrtc.RTPTransceiverDirectionSendonly,
	})
	if err != nil {
		w.logger.Error("Failed to add audio transceiver for viewer", zap.Error(err))
		return fmt.Errorf("failed to add audio transceiver: %w", err)
	}

	// Set remote description (viewer's offer)
	if err := viewerConn.SetRemoteDescription(offer); err != nil {
		return fmt.Errorf("failed to set remote description: %w", err)
	}

	// Parse viewer SDP to get preferred codecs
	w.logger.Info("=== PARSING VIEWER CODEC PREFERENCES ===")
	preferredCodecs := w.parseViewerCodecPreferences(offer.SDP)
	w.logger.Info("Viewer codec preferences parsed",
		zap.String("video_codec", preferredCodecs.videoCodec),
		zap.String("audio_codec", preferredCodecs.audioCodec),
		zap.String("h264_profile", preferredCodecs.h264Profile))

	w.logger.Info("Viewer WebRTC connection created, establishing MediaMTX connection first")

	// Set timeout to send answer even if MediaMTX doesn't connect
	go func() {
		time.Sleep(10 * time.Second)
		w.mutex.Lock()
		if w.viewerAnswer == nil && w.onAnswer != nil {
			w.logger.Warn("Creating emergency answer due to timeout (MediaMTX not connected)")
			// Create emergency answer if MediaMTX connection failed
			if answer, err := viewerConn.CreateAnswer(nil); err == nil {
				if err := viewerConn.SetLocalDescription(answer); err == nil {
					w.onAnswer(answer.SDP)
				}
			}
		}
		w.mutex.Unlock()
	}()

	// Now initiate connection with MediaMTX using viewer codec preferences
	go w.connectToMediaMTXWithCodecs(offer.SDP, streamPath, preferredCodecs)

	return nil
}

// AddViewerICECandidate adds ICE candidate from viewer
func (w *WebRTCProxy) AddViewerICECandidate(candidateStr string) error {
	if w.viewerConnection == nil {
		return fmt.Errorf("viewer connection not initialized")
	}

	candidate := webrtc.ICECandidateInit{
		Candidate: candidateStr,
	}

	if err := w.viewerConnection.AddICECandidate(candidate); err != nil {
		return fmt.Errorf("failed to add ICE candidate: %w", err)
	}

	w.logger.Debug("Added ICE candidate from viewer", zap.String("candidate", candidateStr))
	return nil
}

// connectToMediaMTXWithCodecs establishes connection with MediaMTX using viewer codec preferences
func (w *WebRTCProxy) connectToMediaMTXWithCodecs(offerSDP, streamPath string, preferredCodecs CodecPreferences) {
	w.logger.Info("Connecting to MediaMTX with viewer codec preferences",
		zap.String("stream_path", streamPath),
		zap.String("video_codec", preferredCodecs.videoCodec),
		zap.String("audio_codec", preferredCodecs.audioCodec),
		zap.String("h264_profile", preferredCodecs.h264Profile),
		zap.Int("ice_servers_count", len(MediaMTXICEServers)),
		zap.Bool("has_turn_servers", false),
		zap.String("connection_type", "local_vanilla_ice"))

	// Create simple configuration for MediaMTX connection (minimal ICE servers for local connection)
	config := webrtc.Configuration{
		ICEServers: MediaMTXICEServers, // Minimal config for local MediaMTX connection
	}

	mediaConn, err := webrtc.NewPeerConnection(config)
	if err != nil {
		w.logger.Error("Failed to create MediaMTX peer connection", zap.Error(err))
		if w.onError != nil {
			w.onError(err)
		}
		return
	}
	w.mediaConnection = mediaConn

	// Handle MediaMTX connection state
	mediaConn.OnConnectionStateChange(func(state webrtc.PeerConnectionState) {
		if state == webrtc.PeerConnectionStateConnected || state == webrtc.PeerConnectionStateFailed {
			w.logger.Info("MediaMTX connection state changed", zap.String("state", state.String()))
		}
		if state == webrtc.PeerConnectionStateConnected {
			w.mutex.Lock()
			w.isMediaConnReady = true
			w.mutex.Unlock()
			w.logger.Info("MediaMTX connection established, ready to forward media")
		} else if state == webrtc.PeerConnectionStateFailed {
			w.logger.Error("MediaMTX connection failed")
		}
	})

	// Add ICE connection state handler for MediaMTX - only log important states
	mediaConn.OnICEConnectionStateChange(func(state webrtc.ICEConnectionState) {
		if state == webrtc.ICEConnectionStateConnected || state == webrtc.ICEConnectionStateFailed {
			w.logger.Info("MediaMTX ICE connection state", zap.String("state", state.String()))
		}
	})

	// Handle incoming media tracks from MediaMTX
	mediaConn.OnTrack(func(track *webrtc.TrackRemote, receiver *webrtc.RTPReceiver) {
		w.logger.Info("🎬 RECEIVED TRACK FROM MEDIAMTX",
			zap.String("kind", track.Kind().String()),
			zap.String("codec", track.Codec().MimeType),
			zap.String("track_id", track.ID()),
			zap.String("stream_id", track.StreamID()),
			zap.Uint8("payload_type", uint8(track.PayloadType())),
			zap.String("fmtp_line", track.Codec().SDPFmtpLine))

		// Extract and log H.264 profile if applicable
		if strings.Contains(track.Codec().MimeType, "H264") {
			profile := w.extractMediaMTXProfile(track.Codec().SDPFmtpLine)
			w.logger.Info("📹 H.264 TRACK DETAILS",
				zap.String("profile", profile),
				zap.String("full_fmtp", track.Codec().SDPFmtpLine))
		}

		// Forward track to viewer connection
		go w.forwardTrack(track)
	})

	// Add transceivers for receiving from MediaMTX
	_, err = mediaConn.AddTransceiverFromKind(webrtc.RTPCodecTypeVideo, webrtc.RTPTransceiverInit{
		Direction: webrtc.RTPTransceiverDirectionRecvonly,
	})
	if err != nil {
		w.logger.Error("Failed to add video transceiver", zap.Error(err))
		if w.onError != nil {
			w.onError(err)
		}
		return
	}

	_, err = mediaConn.AddTransceiverFromKind(webrtc.RTPCodecTypeAudio, webrtc.RTPTransceiverInit{
		Direction: webrtc.RTPTransceiverDirectionRecvonly,
	})
	if err != nil {
		w.logger.Error("Failed to add audio transceiver", zap.Error(err))
		if w.onError != nil {
			w.onError(err)
		}
		return
	}

	// Create offer for MediaMTX
	offer, err := mediaConn.CreateOffer(nil)
	if err != nil {
		w.logger.Error("Failed to create offer for MediaMTX", zap.Error(err))
		if w.onError != nil {
			w.onError(err)
		}
		return
	}

	if err := mediaConn.SetLocalDescription(offer); err != nil {
		w.logger.Error("Failed to set local description for MediaMTX", zap.Error(err))
		if w.onError != nil {
			w.onError(err)
		}
		return
	}

	// Wait for ICE gathering to complete for MediaMTX (vanilla ICE)
	go w.waitForICEAndCallWHEP(streamPath, offer.SDP)
}

// waitForICEAndCallWHEP waits for ICE gathering to complete then calls MediaMTX WHEP API
func (w *WebRTCProxy) waitForICEAndCallWHEP(streamPath, offerSDP string) {
	// Wait for ICE gathering to complete (vanilla ICE for MediaMTX)
	for {
		if w.mediaConnection.ICEGatheringState() == webrtc.ICEGatheringStateComplete {
			break
		}
		time.Sleep(100 * time.Millisecond)
	}

	w.logger.Info("ICE gathering complete for MediaMTX, calling WHEP API")

	// Get the complete offer SDP with all ICE candidates
	completeOffer := w.mediaConnection.LocalDescription()
	if completeOffer == nil {
		w.logger.Error("No local description available for WHEP call")
		if w.onError != nil {
			w.onError(fmt.Errorf("no local description available"))
		}
		return
	}

	// Call MediaMTX WHEP API
	answerSDP, err := w.callWHEPAPI(streamPath, completeOffer.SDP)
	if err != nil {
		w.logger.Error("Failed to call WHEP API", zap.Error(err))
		if w.onError != nil {
			w.onError(err)
		}
		return
	}

	// Set remote description (MediaMTX answer)
	answer := webrtc.SessionDescription{
		Type: webrtc.SDPTypeAnswer,
		SDP:  answerSDP,
	}

	// Log MediaMTX SDP for debugging
	w.logger.Info("=== MEDIAMTX SDP COMPARISON ===")
	w.logSDPCodecs("MediaMTX Answer", answerSDP)

	if w.viewerConnection != nil && w.viewerConnection.LocalDescription() != nil {
		w.logSDPCodecs("Viewer Answer (Our Local)", w.viewerConnection.LocalDescription().SDP)
	}

	if err := w.mediaConnection.SetRemoteDescription(answer); err != nil {
		w.logger.Error("Failed to set MediaMTX answer", zap.Error(err))
		if w.onError != nil {
			w.onError(err)
		}
		return
	}

	w.logger.Info("MediaMTX WHEP connection established successfully")
}

// callWHEPAPI calls the MediaMTX WHEP API
func (w *WebRTCProxy) callWHEPAPI(streamPath, offerSDP string) (string, error) {
	// This should be configurable, but for now we'll hardcode localhost
	whepURL := fmt.Sprintf("http://localhost:8889/%s/whep", streamPath)

	w.logger.Info("Calling MediaMTX WHEP API",
		zap.String("url", whepURL),
		zap.String("stream_path", streamPath))

	req, err := http.NewRequest("POST", whepURL, strings.NewReader(offerSDP))
	if err != nil {
		return "", fmt.Errorf("failed to create HTTP request: %w", err)
	}

	req.Header.Set("Content-Type", "application/sdp")
	req.Header.Set("Accept", "application/sdp")

	client := &http.Client{Timeout: 30 * time.Second} // Increased timeout for vanilla ICE
	resp, err := client.Do(req)
	if err != nil {
		return "", fmt.Errorf("failed to send WHEP request: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusCreated {
		body, _ := io.ReadAll(resp.Body)
		return "", fmt.Errorf("WHEP request failed with status %d: %s", resp.StatusCode, string(body))
	}

	answerSDP, err := io.ReadAll(resp.Body)
	if err != nil {
		return "", fmt.Errorf("failed to read answer SDP: %w", err)
	}

	w.logger.Info("WHEP API call successful", zap.String("location", resp.Header.Get("Location")))
	return string(answerSDP), nil
}

// forwardTrack forwards media track from MediaMTX to viewer
func (w *WebRTCProxy) forwardTrack(incomingTrack *webrtc.TrackRemote) {
	if w.viewerConnection == nil {
		w.logger.Error("Cannot forward track: viewer connection not available")
		return
	}

	// Create answer first if not already created (needed for payload type lookup)
	w.mutex.Lock()
	if w.viewerAnswer == nil && w.viewerConnection != nil {
		// Create viewer answer now that we have tracks ready
		if answer, err := w.viewerConnection.CreateAnswer(nil); err == nil {
			if err := w.viewerConnection.SetLocalDescription(answer); err == nil {
				w.viewerAnswer = &answer
				w.logger.Info("Created viewer answer for payload type negotiation")
			}
		}
	}
	w.mutex.Unlock()

	// Extract MediaMTX profile for H264 matching
	mediaMTXProfile := w.extractMediaMTXProfile(incomingTrack.Codec().SDPFmtpLine)

	// Get the negotiated payload type for this track kind from viewer connection
	viewerPayloadType := w.getViewerPayloadType(incomingTrack.Kind(), incomingTrack.Codec().MimeType, mediaMTXProfile)
	mediaPayloadType := uint8(incomingTrack.PayloadType())

	w.logger.Info("Payload type mapping",
		zap.String("kind", incomingTrack.Kind().String()),
		zap.String("mime_type", incomingTrack.Codec().MimeType),
		zap.String("mediamtx_profile", mediaMTXProfile),
		zap.Uint8("media_payload_type", mediaPayloadType),
		zap.Uint8("viewer_payload_type", viewerPayloadType))

	// Find existing transceiver for this track kind and replace the track
	transceivers := w.viewerConnection.GetTransceivers()
	var targetSender *webrtc.RTPSender

	for _, transceiver := range transceivers {
		if transceiver.Kind() == incomingTrack.Kind() && transceiver.Direction() == webrtc.RTPTransceiverDirectionSendonly {
			targetSender = transceiver.Sender()
			w.logger.Info("Found target transceiver for track replacement",
				zap.String("kind", incomingTrack.Kind().String()),
				zap.String("mid", transceiver.Mid()))
			break
		}
	}

	if targetSender == nil {
		w.logger.Error("No suitable transceiver found for track forwarding",
			zap.String("kind", incomingTrack.Kind().String()))
		return
	}

	// Create outgoing track to forward to viewer
	codec := incomingTrack.Codec()

	w.logger.Info("🎯 CREATING OUTGOING TRACK FOR VIEWER",
		zap.String("mime_type", codec.MimeType),
		zap.Uint32("clock_rate", codec.ClockRate),
		zap.Uint16("channels", codec.Channels),
		zap.String("fmtp_line", codec.SDPFmtpLine),
		zap.String("track_id", fmt.Sprintf("%s-forwarded", incomingTrack.ID())),
		zap.String("stream_id", fmt.Sprintf("%s-forwarded", incomingTrack.StreamID())))

	outgoingTrack, err := webrtc.NewTrackLocalStaticRTP(
		webrtc.RTPCodecCapability{
			MimeType:     codec.MimeType,
			ClockRate:    codec.ClockRate,
			Channels:     codec.Channels,
			SDPFmtpLine:  codec.SDPFmtpLine,
			RTCPFeedback: codec.RTCPFeedback,
		},
		fmt.Sprintf("%s-forwarded", incomingTrack.ID()),
		fmt.Sprintf("%s-forwarded", incomingTrack.StreamID()),
	)
	if err != nil {
		w.logger.Error("Failed to create outgoing track", zap.Error(err))
		return
	}

	// Replace track in existing transceiver
	if err := targetSender.ReplaceTrack(outgoingTrack); err != nil {
		w.logger.Error("Failed to replace track in transceiver", zap.Error(err))
		return
	}

	w.logger.Info("✅ TRACK REPLACED IN TRANSCEIVER SUCCESSFULLY",
		zap.String("kind", incomingTrack.Kind().String()),
		zap.String("track_id", outgoingTrack.ID()),
		zap.String("incoming_track_id", incomingTrack.ID()),
		zap.String("incoming_stream_id", incomingTrack.StreamID()),
		zap.String("outgoing_track_id", outgoingTrack.ID()),
		zap.String("codec", incomingTrack.Codec().MimeType))

	// Send answer to viewer now that tracks are ready
	w.mutex.Lock()
	if w.viewerAnswer != nil && w.onAnswer != nil {
		w.logger.Info("Sending viewer answer with tracks ready")
		// Capture values before goroutine to prevent nil pointer
		answerSDP := w.viewerAnswer.SDP
		onAnswerCallback := w.onAnswer
		w.viewerAnswer = nil // Mark as sent before goroutine

		// Add small delay to prevent WebSocket race condition
		go func() {
			time.Sleep(50 * time.Millisecond)
			if onAnswerCallback != nil {
				onAnswerCallback(answerSDP)
			}
		}()
	}
	w.mutex.Unlock()

	w.logger.Info("Track forwarding setup complete, starting RTP packet forwarding",
		zap.String("kind", incomingTrack.Kind().String()),
		zap.Uint8("payload_mapping", mediaPayloadType),
		zap.Uint8("to", viewerPayloadType))

	// Forward RTP packets with payload type translation
	packetCount := 0

	// Add panic recovery for RTP forwarding
	defer func() {
		if r := recover(); r != nil {
			w.logger.Error("🚨 PANIC in RTP packet forwarding",
				zap.Any("panic", r),
				zap.String("track_kind", incomingTrack.Kind().String()),
				zap.String("track_id", incomingTrack.ID()),
				zap.Int("packets_forwarded", packetCount))
		}
	}()

	for {
		// Check if outgoing track is still valid
		if outgoingTrack == nil {
			w.logger.Error("❌ Outgoing track is nil, stopping RTP forwarding")
			break
		}

		packet, _, err := incomingTrack.ReadRTP()
		if err != nil {
			w.logger.Error("Failed to read RTP packet",
				zap.Error(err),
				zap.Int("packets_forwarded", packetCount))
			break
		}

		// Translate payload type if needed
		if mediaPayloadType != viewerPayloadType {
			packet.PayloadType = viewerPayloadType
		}

		if err := outgoingTrack.WriteRTP(packet); err != nil {
			w.logger.Error("Failed to write RTP packet",
				zap.Error(err),
				zap.Int("packets_forwarded", packetCount))
			break
		}

		packetCount++

		// Enhanced logging for first few packets and periodically
		if packetCount <= 10 || packetCount%1000 == 0 {
			// Analyze H.264 NAL units for decode issues
			nalInfo := ""
			if incomingTrack.Kind() == webrtc.RTPCodecTypeVideo && len(packet.Payload) > 4 {
				nalInfo = w.analyzeH264Payload(packet.Payload)
			}

			w.logger.Info("📦 RTP PACKET FORWARDED",
				zap.String("kind", incomingTrack.Kind().String()),
				zap.Int("packet_count", packetCount),
				zap.Int("payload_size", len(packet.Payload)),
				zap.Uint8("original_pt", mediaPayloadType),
				zap.Uint8("mapped_pt", packet.PayloadType),
				zap.Uint32("timestamp", packet.Timestamp),
				zap.Uint16("sequence", packet.SequenceNumber),
				zap.Uint32("ssrc", packet.SSRC),
				zap.Bool("marker", packet.Marker),
				zap.String("payload_prefix", fmt.Sprintf("%02x %02x %02x %02x",
					getByteOrZero(packet.Payload, 0),
					getByteOrZero(packet.Payload, 1),
					getByteOrZero(packet.Payload, 2),
					getByteOrZero(packet.Payload, 3))),
				zap.String("nal_info", nalInfo))
		}
	}

	w.logger.Warn("RTP forwarding loop ended",
		zap.String("kind", incomingTrack.Kind().String()),
		zap.Int("total_packets", packetCount))
}

// getViewerPayloadType finds the best matching payload type for MediaMTX codec in viewer SDP
func (w *WebRTCProxy) getViewerPayloadType(trackKind webrtc.RTPCodecType, mimeType string, mediaMTXProfile string) uint8 {
	if w.viewerConnection == nil {
		w.logger.Warn("No viewer connection available for payload type lookup")
		return 96 // Default dynamic payload type
	}

	// Get the local description (our answer to the viewer)
	localDesc := w.viewerConnection.LocalDescription()
	if localDesc == nil {
		w.logger.Warn("No local description available for payload type lookup")
		return 96 // Default dynamic payload type
	}

	// For H264, we need to find the exact profile match
	if strings.Contains(mimeType, "H264") {
		return w.findH264PayloadTypeByProfile(localDesc.SDP, mediaMTXProfile)
	}

	// Check for codec mismatch - if MediaMTX sends non-H264 but we need H264, or vice versa
	if !strings.Contains(mimeType, "H264") {
		w.logger.Error("🚨 CRITICAL CODEC MISMATCH DETECTED 🚨",
			zap.String("mediamtx_codec", mimeType),
			zap.String("expected", "H264"),
			zap.String("issue", "MediaMTX is sending "+mimeType+" but viewer expects H264"),
			zap.String("solution", "Check MediaMTX transcoding configuration or camera codec settings"))
	}

	// For non-H264 codecs, use simple matching
	lines := strings.Split(localDesc.SDP, "\n")
	var inVideoSection, inAudioSection bool

	for _, line := range lines {
		line = strings.TrimSpace(line)

		// Detect media sections
		if strings.HasPrefix(line, "m=video") {
			inVideoSection = true
			inAudioSection = false
		} else if strings.HasPrefix(line, "m=audio") {
			inVideoSection = false
			inAudioSection = true
		}

		// Skip if not in the right media section
		if (trackKind == webrtc.RTPCodecTypeVideo && !inVideoSection) ||
			(trackKind == webrtc.RTPCodecTypeAudio && !inAudioSection) {
			continue
		}

		// Look for rtpmap lines that match our codec
		if strings.HasPrefix(line, "a=rtpmap:") {
			// Format: a=rtpmap:<payload> <encoding>/<clock>[/<channels>]
			parts := strings.Split(line, " ")
			if len(parts) >= 2 {
				payloadAndColon := strings.Split(parts[0], ":")
				if len(payloadAndColon) >= 2 {
					payloadStr := payloadAndColon[1]
					encodingParts := strings.Split(parts[1], "/")
					if len(encodingParts) >= 1 {
						encoding := strings.ToLower(encodingParts[0])
						expectedEncoding := strings.ToLower(strings.Split(mimeType, "/")[1])

						if encoding == expectedEncoding {
							if payloadType, err := strconv.ParseUint(payloadStr, 10, 8); err == nil {
								w.logger.Info("Found matching payload type in viewer SDP",
									zap.String("mime_type", mimeType),
									zap.String("encoding", encoding),
									zap.Uint8("payload_type", uint8(payloadType)))
								return uint8(payloadType)
							}
						}
					}
				}
			}
		}
	}

	w.logger.Warn("Could not find payload type in viewer SDP, using default",
		zap.String("kind", trackKind.String()),
		zap.String("mime_type", mimeType))
	return 96 // Default dynamic payload type
}

// findH264PayloadTypeByProfile finds H264 payload type that matches MediaMTX profile
func (w *WebRTCProxy) findH264PayloadTypeByProfile(sdp string, mediaMTXProfile string) uint8 {
	lines := strings.Split(sdp, "\n")
	var currentPayloadType uint8
	var inVideoSection bool

	for _, line := range lines {
		line = strings.TrimSpace(line)

		// Detect video section
		if strings.HasPrefix(line, "m=video") {
			inVideoSection = true
		} else if strings.HasPrefix(line, "m=audio") {
			inVideoSection = false
		}

		if !inVideoSection {
			continue
		}

		// Look for H264 rtpmap
		if strings.HasPrefix(line, "a=rtpmap:") && strings.Contains(line, "H264/90000") {
			parts := strings.Split(line, ":")
			if len(parts) >= 2 {
				payloadStr := strings.Split(parts[1], " ")[0]
				if payloadType, err := strconv.ParseUint(payloadStr, 10, 8); err == nil {
					currentPayloadType = uint8(payloadType)
				}
			}
		}

		// Look for fmtp line with profile-level-id that matches MediaMTX
		if strings.HasPrefix(line, "a=fmtp:") && currentPayloadType != 0 {
			// Check for exact match first
			if strings.Contains(line, "profile-level-id="+mediaMTXProfile) {
				w.logger.Info("Found exact H264 profile match in viewer SDP",
					zap.String("mediamtx_profile", mediaMTXProfile),
					zap.Uint8("payload_type", currentPayloadType),
					zap.String("fmtp_line", line))
				return currentPayloadType
			}

			// Check for compatible H264 baseline profiles (42xxxx series)
			if strings.HasPrefix(mediaMTXProfile, "42") && strings.Contains(line, "profile-level-id=42") {
				// Extract viewer profile from fmtp line
				parts := strings.Split(line, "profile-level-id=")
				if len(parts) >= 2 {
					viewerProfile := strings.Split(parts[1], ";")[0]
					viewerProfile = strings.Split(viewerProfile, " ")[0] // Remove any trailing chars

					// Check if this viewer profile exactly matches MediaMTX profile
					if viewerProfile == mediaMTXProfile {
						w.logger.Info("Found exact H264 profile match in viewer SDP",
							zap.String("mediamtx_profile", mediaMTXProfile),
							zap.String("viewer_profile", viewerProfile),
							zap.Uint8("payload_type", currentPayloadType),
							zap.String("fmtp_line", line))
						return currentPayloadType
					}

					// If not exact match, continue searching for exact match first
					// Store this as a compatible fallback but keep looking for exact match
					w.logger.Debug("Found compatible but not exact H264 profile",
						zap.String("mediamtx_profile", mediaMTXProfile),
						zap.String("viewer_profile", viewerProfile),
						zap.Uint8("payload_type", currentPayloadType))
				}
			}
		}
	}

	// If exact match not found, fallback to first H264 codec
	w.logger.Warn("No exact H264 profile match found, using first H264 codec",
		zap.String("mediamtx_profile", mediaMTXProfile))

	// Return first H264 payload type found
	for _, line := range lines {
		if strings.HasPrefix(line, "a=rtpmap:") && strings.Contains(line, "H264/90000") {
			parts := strings.Split(line, ":")
			if len(parts) >= 2 {
				payloadStr := strings.Split(parts[1], " ")[0]
				if payloadType, err := strconv.ParseUint(payloadStr, 10, 8); err == nil {
					return uint8(payloadType)
				}
			}
		}
	}

	return 96 // Default
}

// extractMediaMTXProfile extracts profile-level-id from MediaMTX codec fmtp line
func (w *WebRTCProxy) extractMediaMTXProfile(fmtpLine string) string {
	// Look for profile-level-id parameter in fmtp line
	// Example: "level-asymmetry-allowed=1;packetization-mode=1;profile-level-id=42e01f"
	parts := strings.Split(fmtpLine, ";")
	for _, part := range parts {
		part = strings.TrimSpace(part)
		if strings.HasPrefix(part, "profile-level-id=") {
			profile := strings.TrimPrefix(part, "profile-level-id=")
			w.logger.Info("Extracted MediaMTX H264 profile", zap.String("profile", profile))
			return profile
		}
	}

	w.logger.Warn("No profile-level-id found in MediaMTX fmtp line", zap.String("fmtp", fmtpLine))
	return "" // No profile found
}

// logSDPCodecs logs codec information from SDP for debugging
func (w *WebRTCProxy) logSDPCodecs(label, sdp string) {
	w.logger.Info("=== SDP CODEC ANALYSIS: " + label + " ===")
	lines := strings.Split(sdp, "\n")

	for _, line := range lines {
		line = strings.TrimSpace(line)
		if strings.HasPrefix(line, "a=rtpmap:") || strings.HasPrefix(line, "a=fmtp:") {
			w.logger.Info("SDP Line", zap.String("content", line))
		}
	}
}

// analyzeH264Payload analyzes H.264 RTP payload for decode issues
func (w *WebRTCProxy) analyzeH264Payload(payload []byte) string {
	if len(payload) < 2 {
		return "payload_too_short"
	}

	// Check for H.264 NAL unit header
	nalHeader := payload[0]
	nalType := nalHeader & 0x1F

	switch nalType {
	case 1:
		return "slice_non_idr"
	case 5:
		return "slice_idr" // Key frame
	case 6:
		return "sei"
	case 7:
		return "sps" // Sequence Parameter Set - critical for decode
	case 8:
		return "pps" // Picture Parameter Set - critical for decode
	case 9:
		return "aud" // Access Unit Delimiter
	case 28:
		// FU-A (Fragmentation Unit)
		if len(payload) > 1 {
			fuHeader := payload[1]
			start := (fuHeader & 0x80) != 0
			end := (fuHeader & 0x40) != 0
			nalType := fuHeader & 0x1F
			return fmt.Sprintf("fu-a_nal%d_%s%s", nalType,
				map[bool]string{true: "start_", false: ""}[start],
				map[bool]string{true: "end", false: ""}[end])
		}
		return "fu-a_invalid"
	default:
		return fmt.Sprintf("nal_type_%d", nalType)
	}
}

// getByteOrZero safely gets byte at index or returns 0
func getByteOrZero(data []byte, index int) byte {
	if index < len(data) {
		return data[index]
	}
	return 0
}

// Close cleanup connections
func (w *WebRTCProxy) Close() {
	w.logger.Info("Closing WebRTC proxy")

	if w.viewerConnection != nil {
		w.viewerConnection.Close()
	}
	if w.mediaConnection != nil {
		w.mediaConnection.Close()
	}
}

// ConnectionStats returns connection statistics
func (w *WebRTCProxy) ConnectionStats() map[string]interface{} {
	stats := make(map[string]interface{})

	if w.viewerConnection != nil {
		stats["viewer_state"] = w.viewerConnection.ConnectionState().String()
	}
	if w.mediaConnection != nil {
		stats["media_state"] = w.mediaConnection.ConnectionState().String()
	}

	w.mutex.RLock()
	stats["media_ready"] = w.isMediaConnReady
	w.mutex.RUnlock()

	return stats
}
