package main

import (
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"os"
	"os/signal"
	"strings"
	"syscall"
	"time"

	"github.com/gorilla/websocket"
	"go.uber.org/zap"
)

type MessageType string

const (
	// Publisher messages
	PublisherRegister MessageType = "publisher_register"
	PublisherStreams  MessageType = "publisher_streams"
	PublisherAnswer   MessageType = "publisher_answer"

	// Server messages
	WebRTCOffer MessageType = "webrtc_offer"
)

type WebSocketMessage struct {
	Type MessageType `json:"type"`
	Data interface{} `json:"data"`
}

type PublisherStreamsData struct {
	Streams []string `json:"streams"`
}

type WebRTCOfferData struct {
	StreamPath string `json:"stream_path"`
	SDP        string `json:"sdp"`
	ViewerID   string `json:"viewer_id"`
}

type PublisherAnswerData struct {
	ViewerID string `json:"viewer_id"`
	SDP      string `json:"sdp"`
}

type MediaMTXListResponse struct {
	Items map[string]MediaMTXPath `json:"items"`
}

type MediaMTXPath struct {
	Name          string        `json:"name"`
	SourceReady   bool          `json:"sourceReady"`
	Tracks        []interface{} `json:"tracks"`
	BytesReceived int64         `json:"bytesReceived"`
	BytesSent     int64         `json:"bytesSent"`
}

type Publisher struct {
	logger         *zap.Logger
	serverURL      string
	mediamtxURL    string
	conn           *websocket.Conn
	managedStreams []string
	reconnectDelay time.Duration
	ctx            context.Context
	cancel         context.CancelFunc
}

func NewPublisher(serverURL, mediamtxURL string, managedStreams []string) *Publisher {
	logger, _ := zap.NewProduction()
	ctx, cancel := context.WithCancel(context.Background())

	return &Publisher{
		logger:         logger,
		serverURL:      serverURL,
		mediamtxURL:    mediamtxURL,
		managedStreams: managedStreams,
		reconnectDelay: 5 * time.Second,
		ctx:            ctx,
		cancel:         cancel,
	}
}

func (p *Publisher) Start() error {
	p.logger.Info("Starting publisher",
		zap.String("server_url", p.serverURL),
		zap.String("mediamtx_url", p.mediamtxURL),
		zap.Strings("managed_streams", p.managedStreams))

	for {
		select {
		case <-p.ctx.Done():
			return nil
		default:
			if err := p.connectAndRun(); err != nil {
				p.logger.Error("Connection error", zap.Error(err))
				select {
				case <-p.ctx.Done():
					return nil
				case <-time.After(p.reconnectDelay):
					p.logger.Info("Attempting to reconnect...")
					continue
				}
			}
		}
	}
}

func (p *Publisher) Stop() {
	p.logger.Info("Stopping publisher")
	p.cancel()
	if p.conn != nil {
		p.conn.Close()
	}
}

func (p *Publisher) connectAndRun() error {
	u, err := url.Parse(p.serverURL)
	if err != nil {
		return fmt.Errorf("invalid server URL: %w", err)
	}

	p.logger.Info("Connecting to server", zap.String("url", u.String()))

	conn, _, err := websocket.DefaultDialer.Dial(u.String(), nil)
	if err != nil {
		return fmt.Errorf("failed to connect to server: %w", err)
	}
	p.conn = conn

	defer func() {
		p.conn.Close()
		p.conn = nil
	}()

	p.logger.Info("Connected to server")

	// Register as publisher
	if err := p.sendMessage(WebSocketMessage{
		Type: PublisherRegister,
		Data: nil,
	}); err != nil {
		return fmt.Errorf("failed to register as publisher: %w", err)
	}

	// Send managed streams
	if err := p.sendStreamsUpdate(); err != nil {
		return fmt.Errorf("failed to send streams update: %w", err)
	}

	// Start message handling loop
	return p.messageLoop()
}

func (p *Publisher) sendStreamsUpdate() error {
	// For now, we use the configured managed streams
	// In a real implementation, you might query MediaMTX API to get available streams
	data := PublisherStreamsData{
		Streams: p.managedStreams,
	}

	return p.sendMessage(WebSocketMessage{
		Type: PublisherStreams,
		Data: data,
	})
}

func (p *Publisher) messageLoop() error {
	for {
		select {
		case <-p.ctx.Done():
			return nil
		default:
			var msg WebSocketMessage
			if err := p.conn.ReadJSON(&msg); err != nil {
				if websocket.IsUnexpectedCloseError(err, websocket.CloseGoingAway, websocket.CloseAbnormalClosure) {
					return fmt.Errorf("websocket read error: %w", err)
				}
				return nil
			}

			p.logger.Info("Received message", zap.String("type", string(msg.Type)))

			if err := p.handleMessage(msg); err != nil {
				p.logger.Error("Error handling message", zap.Error(err))
			}
		}
	}
}

func (p *Publisher) handleMessage(msg WebSocketMessage) error {
	switch msg.Type {
	case WebRTCOffer:
		return p.handleWebRTCOffer(msg.Data)
	default:
		p.logger.Warn("Unknown message type", zap.String("type", string(msg.Type)))
	}
	return nil
}

func (p *Publisher) handleWebRTCOffer(data interface{}) error {
	dataBytes, _ := json.Marshal(data)
	var offerData WebRTCOfferData
	if err := json.Unmarshal(dataBytes, &offerData); err != nil {
		return fmt.Errorf("failed to parse WebRTC offer data: %w", err)
	}

	p.logger.Info("Received WebRTC offer",
		zap.String("stream_path", offerData.StreamPath),
		zap.String("viewer_id", offerData.ViewerID))

	// Call MediaMTX WHEP API to get answer SDP
	answerSDP, err := p.createWHEPSession(offerData.StreamPath, offerData.SDP)
	if err != nil {
		p.logger.Error("Failed to create WHEP session",
			zap.String("stream_path", offerData.StreamPath),
			zap.Error(err))
		return err
	}

	// Send answer back to server
	return p.sendMessage(WebSocketMessage{
		Type: PublisherAnswer,
		Data: PublisherAnswerData{
			ViewerID: offerData.ViewerID,
			SDP:      answerSDP,
		},
	})
}

func (p *Publisher) createWHEPSession(streamPath, offerSDP string) (string, error) {
	// MediaMTX WHEP endpoint URL
	whepURL := fmt.Sprintf("%s/%s/whep", p.mediamtxURL, streamPath)

	p.logger.Info("Creating WHEP session",
		zap.String("url", whepURL),
		zap.String("stream_path", streamPath))

	// Create HTTP request with offer SDP
	req, err := http.NewRequest("POST", whepURL, strings.NewReader(offerSDP))
	if err != nil {
		return "", fmt.Errorf("failed to create HTTP request: %w", err)
	}

	req.Header.Set("Content-Type", "application/sdp")
	req.Header.Set("Accept", "application/sdp")

	// Send request to MediaMTX
	client := &http.Client{Timeout: 10 * time.Second}
	resp, err := client.Do(req)
	if err != nil {
		return "", fmt.Errorf("failed to send WHEP request: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusCreated {
		body, _ := io.ReadAll(resp.Body)
		return "", fmt.Errorf("WHEP request failed with status %d: %s", resp.StatusCode, string(body))
	}

	// Read answer SDP from response
	answerSDP, err := io.ReadAll(resp.Body)
	if err != nil {
		return "", fmt.Errorf("failed to read answer SDP: %w", err)
	}

	p.logger.Info("WHEP session created successfully",
		zap.String("stream_path", streamPath),
		zap.String("location", resp.Header.Get("Location")))

	return string(answerSDP), nil
}

func (p *Publisher) sendMessage(msg WebSocketMessage) error {
	if p.conn == nil {
		return fmt.Errorf("websocket connection not established")
	}

	p.logger.Debug("Sending message", zap.String("type", string(msg.Type)))

	return p.conn.WriteJSON(msg)
}

// queryMediaMTXStreams queries MediaMTX API to get available streams
func (p *Publisher) queryMediaMTXStreams() ([]string, error) {
	apiURL := fmt.Sprintf("%s/v3/paths/list", p.mediamtxURL)

	resp, err := http.Get(apiURL)
	if err != nil {
		return nil, fmt.Errorf("failed to query MediaMTX API: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("MediaMTX API returned status %d", resp.StatusCode)
	}

	var listResp MediaMTXListResponse
	if err := json.NewDecoder(resp.Body).Decode(&listResp); err != nil {
		return nil, fmt.Errorf("failed to decode MediaMTX response: %w", err)
	}

	var streams []string
	for pathName, pathInfo := range listResp.Items {
		if pathInfo.SourceReady && len(pathInfo.Tracks) > 0 {
			streams = append(streams, pathName)
		}
	}

	return streams, nil
}

func main() {
	// Configuration - in a real deployment, these would come from environment variables or config file
	serverURL := "ws://localhost:8080/ws"
	mediamtxURL := "http://localhost:8889"
	managedStreams := []string{"stream-1"} // Default managed streams

	// Override from environment variables if available
	if envServerURL := os.Getenv("CAM_SERVER_URL"); envServerURL != "" {
		serverURL = envServerURL
	}
	if envMediaMTXURL := os.Getenv("MEDIAMTX_URL"); envMediaMTXURL != "" {
		mediamtxURL = envMediaMTXURL
	}

	publisher := NewPublisher(serverURL, mediamtxURL, managedStreams)

	// Handle graceful shutdown
	c := make(chan os.Signal, 1)
	signal.Notify(c, os.Interrupt, syscall.SIGTERM)

	go func() {
		<-c
		publisher.logger.Info("Received shutdown signal")
		publisher.Stop()
	}()

	// Start publisher
	if err := publisher.Start(); err != nil {
		publisher.logger.Fatal("Publisher failed", zap.Error(err))
	}

	publisher.logger.Info("Publisher stopped")
}
