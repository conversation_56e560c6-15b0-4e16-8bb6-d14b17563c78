import { atom } from 'jotai';
import type { Stream, WebSocketMessage, MessageType } from '../types/stream';

export const websocketAtom = atom<WebSocket | null>(null);
export const streamsAtom = atom<Stream[]>([]);
export const connectionStatusAtom = atom<'connecting' | 'connected' | 'disconnected'>('disconnected');
export const currentServerUrlAtom = atom<string | null>(null);
export const lastServerUrlAtom = atom<string | null>(null); // Keep last URL for manual reconnect
export const reconnectAttemptAtom = atom<number>(0);

// Global reference to current get/set functions for direct calls
let globalGet: any = null;
let globalSet: any = null;

// Create a regular function for connection logic
const createWebSocketConnection = (get: any, set: any, serverUrl: string) => {
  // Store global references for direct calls
  globalGet = get;
  globalSet = set;
  const existingWs = get(websocketAtom);
  const currentUrl = get(currentServerUrlAtom);
  const connectionStatus = get(connectionStatus<PERSON>tom);

  // If already connected to the same URL, don't reconnect
  if (
    currentUrl === serverUrl &&
    connectionStatus === 'connected' &&
    existingWs &&
    existingWs.readyState === WebSocket.OPEN
  ) {
    return;
  }

  // If currently connecting to the same URL, don't start another connection
  if (currentUrl === serverUrl && connectionStatus === 'connecting') {
    return;
  }

  // If there's an existing connection, close it first
  if (existingWs && (existingWs.readyState === WebSocket.OPEN || existingWs.readyState === WebSocket.CONNECTING)) {
    existingWs.close(1000, 'Reconnecting');
  }

  set(connectionStatusAtom, 'connecting');
  set(currentServerUrlAtom, serverUrl);
  set(lastServerUrlAtom, serverUrl); // Remember last URL

  let ws: WebSocket;
  try {
    ws = new WebSocket(serverUrl);
  } catch (error) {
    console.error('Failed to create WebSocket:', error);
    set(connectionStatusAtom, 'disconnected');
    set(currentServerUrlAtom, null);
    return;
  }

  // Set a connection timeout
  const connectionTimeout = setTimeout(() => {
    if (ws.readyState === WebSocket.CONNECTING) {
      console.log('WebSocket connection timeout');
      ws.close();
    }
  }, 10000);

  ws.onopen = () => {
    clearTimeout(connectionTimeout);
    console.log('WebSocket connected');
    set(connectionStatusAtom, 'connected');
    set(reconnectAttemptAtom, 0);

    const message: WebSocketMessage = {
      type: 'viewer_register',
      data: null,
    };
    ws.send(JSON.stringify(message));
  };

  ws.onmessage = (event) => {
    try {
      const message: WebSocketMessage = JSON.parse(event.data);

      switch (message.type) {
        case 'streams_list':
          set(streamsAtom, message.data as Stream[]);
          break;
        case 'stream_status_update':
          set(streamsAtom, message.data as Stream[]);
          break;
        case 'webrtc_answer':
          window.dispatchEvent(new CustomEvent('webrtc-answer', { detail: message.data }));
          break;
        case 'ice_candidate':
          window.dispatchEvent(new CustomEvent('ice-candidate', { detail: message.data }));
          break;
      }
    } catch (error) {
      console.error('Error parsing WebSocket message:', error);
    }
  };

  ws.onclose = (event) => {
    clearTimeout(connectionTimeout);
    console.log('WebSocket disconnected:', event.code, event.reason);
    set(connectionStatusAtom, 'disconnected');
    set(websocketAtom, null);
    set(currentServerUrlAtom, null);

    // Only auto-reconnect if it wasn't a manual close
    if (event.code !== 1000) {
      const currentAttempt = get(reconnectAttemptAtom);
      const maxAttempts = 5;

      if (currentAttempt < maxAttempts) {
        // Exponential backoff: 3s, 6s, 12s, 24s, 48s
        const delay = Math.min(3000 * Math.pow(2, currentAttempt), 30000);
        console.log(`Reconnecting in ${delay}ms (attempt ${currentAttempt + 1}/${maxAttempts})`);

        setTimeout(() => {
          if (globalGet && globalSet) {
            const currentStatus = globalGet(connectionStatusAtom);
            const lastUrl = globalGet(lastServerUrlAtom);
            if (currentStatus === 'disconnected' && lastUrl) {
              globalSet(reconnectAttemptAtom, currentAttempt + 1);
              createWebSocketConnection(globalGet, globalSet, lastUrl);
            }
          }
        }, delay);
      } else {
        console.log('Max reconnection attempts reached');
      }
    }
  };

  ws.onerror = (error) => {
    clearTimeout(connectionTimeout);
    console.error('WebSocket error:', error);
    set(connectionStatusAtom, 'disconnected');
  };

  set(websocketAtom, ws);
};

export const connectWebSocketAtom = atom(null, (get, set, serverUrl: string) => {
  createWebSocketConnection(get, set, serverUrl);
});

export const sendWebSocketMessageAtom = atom(null, (get, set, message: WebSocketMessage) => {
  const ws = get(websocketAtom);
  if (ws && ws.readyState === WebSocket.OPEN) {
    ws.send(JSON.stringify(message));
  } else {
    console.error('WebSocket not connected');
  }
});

export const manualReconnectAtom = atom(null, (get, set) => {
  const lastUrl = get(lastServerUrlAtom);
  if (lastUrl) {
    console.log('Manual reconnect triggered');
    set(reconnectAttemptAtom, 0);
    set(connectWebSocketAtom, lastUrl);
  } else {
    console.error('No server URL available for manual reconnect');
  }
});
