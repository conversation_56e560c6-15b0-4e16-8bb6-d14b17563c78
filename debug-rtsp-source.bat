@echo off
echo 🔍 Debugging RTSP Source and MediaMTX...

echo.
echo 📡 Step 1: Check RTSP source directly
echo Testing RTSP connection to camera...
ffprobe -v quiet -print_format json -show_streams "rtsp://rtsp:Xtthanhhoa123@**************:554/Streaming/Channels/101?transportmode=unicast&profile=Profile_1" > rtsp_info.json 2>&1

if %ERRORLEVEL% equ 0 (
    echo ✅ RTSP source is accessible
    echo Video stream info:
    type rtsp_info.json | findstr "codec_name\|width\|height\|pix_fmt\|profile"
) else (
    echo ❌ RTSP source is NOT accessible
    echo This could be the root cause of muted video tracks!
)

echo.
echo 📊 Step 2: Check MediaMTX API
curl -s "http://localhost:8889/v3/paths/list" > mediamtx_paths.json 2>&1
if %ERRORLEVEL% equ 0 (
    echo ✅ MediaMTX API is accessible
    echo Stream status:
    type mediamtx_paths.json | findstr "sourceReady\|tracks"
) else (
    echo ❌ MediaMTX API is not accessible
)

echo.
echo 🎬 Step 3: Test MediaMTX WHEP endpoint
curl -s -X POST -H "Content-Type: application/sdp" -H "Accept: application/sdp" -d "v=0" "http://localhost:8889/stream-1/whep" > whep_test.txt 2>&1
if %ERRORLEVEL% equ 0 (
    echo ✅ WHEP endpoint responds
    echo Response:
    type whep_test.txt
) else (
    echo ❌ WHEP endpoint not responding
)

echo.
echo 🔧 Step 4: Potential fixes based on findings:
echo.
echo If RTSP source fails:
echo   - Check camera network connectivity
echo   - Verify RTSP credentials
echo   - Test with VLC: vlc "rtsp://rtsp:Xtthanhhoa123@**************:554/Streaming/Channels/101?transportmode=unicast&profile=Profile_1"
echo.
echo If MediaMTX has no tracks:
echo   - Stream may not be ready
echo   - Check MediaMTX logs
echo   - Try sourceOnDemand: no in config
echo.
echo If WHEP fails:
echo   - MediaMTX WebRTC not enabled
echo   - Check mediamtx.yml configuration
echo.
pause
