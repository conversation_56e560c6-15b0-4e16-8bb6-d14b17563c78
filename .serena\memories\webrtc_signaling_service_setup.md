# WebRTC Signaling Service Setup

## Service Information
- **Service Name**: `webrtc-signaling`
- **Binary Location**: `/home/<USER>/webrtc-signaling/server`
- **Working Directory**: `/home/<USER>/webrtc-signaling`
- **Run as User**: `tyris` (group: `tyris`)
- **Port**: `8612` (via `CAM_SERVER_PORT` environment variable)

## Installation Scripts
- `install-service.sh`: Installs the service as systemd service
- `remove-service.sh`: Removes the systemd service
- Both scripts are located in the server directory alongside the binary

## Service Configuration
The systemd service file is located at `/etc/systemd/system/webrtc-signaling.service`

### Key Settings
- **ExecStart**: Uses absolute path to binary
- **Environment**: `CAM_SERVER_PORT=8612`
- **Restart**: Always restart on failure with 5 second delay
- **User/Group**: Runs as current user (tyris), not root
- **Security Settings**: Removed due to compatibility issues with the Go binary

### Known Issues & Solutions
1. **Security Settings**: The original security hardening settings caused exec failures (status 203/EXEC)
   - Solution: Comment out or remove all ProtectSystem, ProtectHome, etc. settings
2. **Duplicate Environment**: Having duplicate Environment lines can cause issues
   - Solution: Consolidate all environment variables into single Environment lines
3. **User Permissions**: Using system users caused "Failed to determine user credentials" error
   - Solution: Use actual user account instead of creating system users

## Service Management Commands
```bash
# Start service
sudo systemctl start webrtc-signaling

# Stop service  
sudo systemctl stop webrtc-signaling

# Restart service
sudo systemctl restart webrtc-signaling

# Check status
sudo systemctl status webrtc-signaling

# View logs
sudo journalctl -u webrtc-signaling -f

# Install service
sudo ./install-service.sh

# Remove service
sudo ./remove-service.sh
```

## Manual Server Command
The server can be run manually with:
```bash
CAM_SERVER_PORT=8612 ./server
```

This is equivalent to what the service runs automatically.