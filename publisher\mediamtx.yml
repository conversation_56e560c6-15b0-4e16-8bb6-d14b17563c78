api: yes

webrtc: yes
webrtcAllowOrigin: '*'
webrtcHandshakeTimeout: 30s
webrtcTrackGatherTimeout: 30s
webrtcSTUNGatherTimeout: 30s
webrtcICEServers2:
  - url: stun:stun.l.google.com:19302
  - url: stun:stun1.l.google.com:19302
  # - url: stun:stun.cloudflare.com:3478
  # - url: stun:stun.cloudflare.com:53
  # - url: turn:turn.cloudflare.com:3478?transport=udp
  #   username: g00568252dfd6a1c8dc61fa75946de166f3975c09996517fb8d47ed7d8ceb85d
  #   password: dc3be9bfad2e6ab18bef259795b6d3d3c55a24d973e21427e0c12e666440f716
  # - url: turn:turn.cloudflare.com:3478?transport=tcp
  #   username: g00568252dfd6a1c8dc61fa75946de166f3975c09996517fb8d47ed7d8ceb85d
  #   password: dc3be9bfad2e6ab18bef259795b6d3d3c55a24d973e21427e0c12e666440f716
  # - url: turns:turn.cloudflare.com:5349?transport=tcp
  #   username: g00568252dfd6a1c8dc61fa75946de166f3975c09996517fb8d47ed7d8ceb85d
  #   password: dc3be9bfad2e6ab18bef259795b6d3d3c55a24d973e21427e0c12e666440f716
  # - url: turn:turn.cloudflare.com:53?transport=udp
  #   username: g00568252dfd6a1c8dc61fa75946de166f3975c09996517fb8d47ed7d8ceb85d
  #   password: dc3be9bfad2e6ab18bef259795b6d3d3c55a24d973e21427e0c12e666440f716
  # - url: turn:turn.cloudflare.com:80?transport=tcp
  #   username: g00568252dfd6a1c8dc61fa75946de166f3975c09996517fb8d47ed7d8ceb85d
  #   password: dc3be9bfad2e6ab18bef259795b6d3d3c55a24d973e21427e0c12e666440f716
  # - url: turns:turn.cloudflare.com:443?transport=tcp
  #   username: g00568252dfd6a1c8dc61fa75946de166f3975c09996517fb8d47ed7d8ceb85d
  #   password: dc3be9bfad2e6ab18bef259795b6d3d3c55a24d973e21427e0c12e666440f716

paths:
  stream-1:
    source: rtsp://rtsp:Xtthanhhoa123@192.168.10.113:554/Streaming/Channels/101?transportmode=unicast&profile=Profile_1
    sourceOnDemand: yes

# Default path settings
pathDefaults:
  # Disable recording by default
  record: no
  
  # Disable authentication for other paths
  publishUser: 
  publishPass: 
  readUser: 
  readPass: