import { useEffect, useState } from 'react';
import { useSet<PERSON>tom } from 'jotai';
import type { Route } from './+types/home';
import { connectWebSocketAtom } from '../store/websocket';
import { StreamList } from '../components/stream-list';
import { StreamViewer } from '../components/stream-viewer';
import type { Stream } from '../types/stream';

export function meta({}: Route.MetaArgs) {
  return [{ title: 'Camera Streaming Viewer' }, { name: 'description', content: 'View camera streams via WebRTC' }];
}

export default function Home() {
  const connectWebSocket = useSetAtom(connectWebSocketAtom);
  const [selectedStream, setSelectedStream] = useState<Stream | null>(null);

  useEffect(() => {
    // Connect to WebSocket server
    const serverUrl = 'wss://signaling.tyris.dev/ws';
    console.log('Home component useEffect triggered, connecting to:', serverUrl);
    connectWebSocket(serverUrl);

    // Cleanup on unmount
    return () => {
      console.log('Home component cleanup');
    };
  }, []); // Remove connectWebSocket from dependencies

  const handleStreamSelect = (stream: Stream) => {
    setSelectedStream(stream);
  };

  const handleBack = () => {
    setSelectedStream(null);
  };

  if (selectedStream) {
    return <StreamViewer stream={selectedStream} onBack={handleBack} />;
  }

  return <StreamList onStreamSelect={handleStreamSelect} />;
}
