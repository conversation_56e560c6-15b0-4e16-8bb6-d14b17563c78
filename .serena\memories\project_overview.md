# Camera Streaming Project Overview

## Purpose
A camera streaming project that enables viewing streams from MediaMTX via WebRTC. The project consists of three main components:

1. **Server**: Go + Fiber + WebSocket - manages connections between publisher and viewer
2. **Publisher**: Go client - connects to MediaMTX and forwards WebRTC
3. **Viewer**: React SPA - web interface for viewing streams

## Architecture
- **WebRTC-based streaming** via MediaMTX server
- **WebSocket communication** between all components
- **Three-tier architecture**: Publisher → Server → Viewer
- **Cross-platform builds** supported for Windows, Linux, macOS, ARM64

## Workflow
1. Publisher connects to Server via WebSocket and registers streams
2. Viewer connects to Server and receives stream list
3. User selects stream on Viewer
4. Viewer creates WebRTC offer and sends via Server
5. Server forwards offer to Publisher managing that stream
6. Publisher calls MediaMTX WHEP API to create session and receive answer
7. Publisher sends answer back to Viewer via Server
8. Viewer receives answer and starts video stream

## Default Configuration
- **Server**: `http://localhost:8080`
- **Viewer**: `http://localhost:3000`
- **MediaMTX**: `http://localhost:8889`
- **Default stream**: `stream-1` - "Cam cân heo"

## Key Dependencies
- **MediaMTX server** (required external dependency)
- **Docker support** available for all components