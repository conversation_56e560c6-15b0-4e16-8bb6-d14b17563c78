{"permissions": {"allow": ["mcp__context7__resolve-library-id", "mcp__context7__get-library-docs", "mcp__serena__get_current_config", "mcp__serena__check_onboarding_performed", "mcp__serena__onboarding", "mcp__serena__list_dir", "mcp__serena__get_symbols_overview", "mcp__serena__find_symbol", "mcp__serena__write_memory", "mcp__serena__search_for_pattern", "mcp__serena__read_memory", "mcp__serena__think_about_task_adherence", "mcp__serena__find_file"], "deny": [], "ask": []}, "enableAllProjectMcpServers": true, "enabledMcpjsonServers": ["serena"]}