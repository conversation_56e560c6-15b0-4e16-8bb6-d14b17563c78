@echo off
echo Building server...
go mod tidy

if "%1"=="" (
    echo Building for Windows...
    go build -o server.exe .
    echo Server built successfully as server.exe
) else if "%1"=="linux" (
    echo Building for Linux...
    set GOOS=linux
    set GOARCH=amd64
    go build -o server .
    echo Server built successfully as server ^(Linux^)
) else if "%1"=="mac" (
    echo Building for macOS...
    set GOOS=darwin
    set GOARCH=amd64
    go build -o server .
    echo Server built successfully as server ^(macOS^)
) else if "%1"=="arm64" (
    echo Building for Linux ARM64...
    set GOOS=linux
    set GOARCH=arm64
    go build -o server .
    echo Server built successfully as server ^(Linux ARM64^)
) else (
    echo Usage: build.bat [linux^|mac^|arm64]
    echo   No argument: Build for Windows
    echo   linux:       Build for Linux x64
    echo   mac:         Build for macOS x64
    echo   arm64:       Build for Linux ARM64
)