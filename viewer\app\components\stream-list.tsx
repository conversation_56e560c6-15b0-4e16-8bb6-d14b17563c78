import { useAtomValue, useSet<PERSON>tom } from 'jotai';
import { Play, Wifi, WifiOff, RefreshCw } from 'lucide-react';
import { streamsAtom, connectionStatusAtom, manualReconnectAtom } from '../store/websocket';
import type { Stream } from '../types/stream';

interface StreamListProps {
  onStreamSelect: (stream: Stream) => void;
}

export function StreamList({ onStreamSelect }: StreamListProps) {
  const streams = useAtomValue(streamsAtom);
  const connectionStatus = useAtomValue(connectionStatusAtom);
  const manualReconnect = useSetAtom(manualReconnectAtom);

  return (
    <div className="w-full max-w-2xl mx-auto p-6">
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center gap-3">
          <h1 className="text-2xl font-bold">Camera Streams</h1>
          <div className="flex items-center gap-2">
            {connectionStatus === 'connected' ? (
              <Wifi className="h-5 w-5 text-green-500" />
            ) : (
              <WifiOff className="h-5 w-5 text-red-500" />
            )}
            <span className="text-sm text-gray-600 capitalize">{connectionStatus}</span>
          </div>
        </div>
        {connectionStatus === 'disconnected' && (
          <button
            onClick={manualReconnect}
            className="flex items-center gap-2 px-3 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors text-sm"
          >
            <RefreshCw className="h-4 w-4" />
            Reconnect
          </button>
        )}
      </div>

      {streams.length === 0 ? (
        <div className="text-center py-12 text-gray-500">
          <p>No streams available</p>
          {connectionStatus === 'disconnected' && <p className="text-sm mt-2">Waiting for connection...</p>}
        </div>
      ) : (
        <div className="grid gap-4">
          {streams.map((stream) => (
            <div key={stream.path} className="border rounded-lg p-4 hover:bg-gray-50 transition-colors">
              <div className="flex items-center justify-between">
                <div>
                  <h3 className="font-semibold text-lg">{stream.name}</h3>
                  <p className="text-sm text-gray-600">Path: {stream.path}</p>
                  <div className="flex items-center gap-2 mt-2">
                    <div className={`w-2 h-2 rounded-full ${stream.active ? 'bg-green-500' : 'bg-red-500'}`} />
                    <span className="text-sm">{stream.active ? 'Active' : 'Inactive'}</span>
                  </div>
                </div>
                <button
                  onClick={() => onStreamSelect(stream)}
                  disabled={!stream.active || connectionStatus !== 'connected'}
                  className="flex items-center gap-2 px-4 py-2 bg-blue-500 text-white rounded-lg disabled:bg-gray-300 disabled:cursor-not-allowed hover:bg-blue-600 transition-colors"
                >
                  <Play className="h-4 w-4" />
                  Watch
                </button>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
}
