# Code Style and Conventions

## Frontend (TypeScript/React)
### Prettier Configuration
- **Print Width**: 120 characters
- **Tab Width**: 2 spaces
- **Use Tabs**: false
- **Single Quotes**: true (except JSX)
- **Trailing Commas**: all
- **Arrow Parens**: always
- **Semicolons**: true
- **Bracket Spacing**: true
- **JSX Bracket Same Line**: false
- **End of Line**: LF

### React Router v7 Patterns
- **File-based routing** in `app/routes/` directory
- **SPA mode** enabled (ssr: false)
- **Component structure**: Functional components with hooks
- **State management**: Jotai atoms for global state
- **Styling**: TailwindCSS with utility classes

### TypeScript Conventions
- **Strict mode** enabled
- **Type definitions** in `app/types/` directory
- **Interface naming**: PascalCase (e.g., `Stream`, `WebSocketMessage`)
- **Component props**: Destructured parameters
- **Hooks**: Custom hooks for WebSocket management

## Backend (Go)
### Project Structure
- **Module naming**: `cam-streaming-server`, `cam-streaming-publisher`
- **Go version**: 1.21
- **File organization**: Single `main.go` for simple services

### Logging Conventions
- **Zap logger** with structured logging
- **Log levels**: Debug, Info, Error
- **Context-aware logging** for WebSocket connections

### WebSocket Message Format
- **JSON-based** message structure
- **Type field** for message routing
- **Data payload** with typed structures

## General Conventions
### Naming
- **Directories**: lowercase with hyphens
- **Go files**: snake_case
- **TypeScript files**: kebab-case
- **Components**: PascalCase

### Documentation
- **README files** with Vietnamese comments
- **Inline comments** in English for code
- **API documentation** via WebSocket message examples