package main

import (
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"os"
	"os/signal"
	"strings"
	"sync"
	"syscall"
	"time"

	"github.com/gorilla/websocket"
	"github.com/pion/webrtc/v3"
	"go.uber.org/zap"
)

type MessageType string

const (
	// Publisher messages
	PublisherRegister     MessageType = "publisher_register"
	PublisherStreams      MessageType = "publisher_streams"
	PublisherAnswer       MessageType = "publisher_answer"
	PublisherICECandidate MessageType = "publisher_ice_candidate"

	// Server messages
	WebRTCOffer        MessageType = "webrtc_offer"
	ViewerICECandidate MessageType = "viewer_ice_candidate"
)

type WebSocketMessage struct {
	Type MessageType `json:"type"`
	Data interface{} `json:"data"`
}

type PublisherStreamsData struct {
	Streams []string `json:"streams"`
}

type WebRTCOfferData struct {
	StreamPath string `json:"stream_path"`
	SDP        string `json:"sdp"`
	ViewerID   string `json:"viewer_id"`
}

type PublisherAnswerData struct {
	ViewerID string `json:"viewer_id"`
	SDP      string `json:"sdp"`
}

type ICECandidateData struct {
	ViewerID      string `json:"viewer_id"`
	Candidate     string `json:"candidate"`
	SDPMid        string `json:"sdp_mid"`
	SDPMLineIndex int    `json:"sdp_mline_index"`
}

type MediaMTXListResponse struct {
	Items map[string]MediaMTXPath `json:"items"`
}

type MediaMTXPath struct {
	Name          string        `json:"name"`
	SourceReady   bool          `json:"sourceReady"`
	Tracks        []interface{} `json:"tracks"`
	BytesReceived int64         `json:"bytesReceived"`
	BytesSent     int64         `json:"bytesSent"`
}

type Publisher struct {
	logger         *zap.Logger
	serverURL      string
	mediamtxURL    string
	conn           *websocket.Conn
	connMutex      sync.Mutex // Protect WebSocket writes
	managedStreams []string
	reconnectDelay time.Duration
	ctx            context.Context
	cancel         context.CancelFunc
	webrtcProxies  map[string]*WebRTCProxy // viewerID -> WebRTC proxy
	proxiesMutex   sync.RWMutex
}

func NewPublisher(serverURL, mediamtxURL string, managedStreams []string) *Publisher {
	logger, _ := zap.NewProduction()
	ctx, cancel := context.WithCancel(context.Background())

	return &Publisher{
		logger:         logger,
		serverURL:      serverURL,
		mediamtxURL:    mediamtxURL,
		managedStreams: managedStreams,
		reconnectDelay: 5 * time.Second,
		ctx:            ctx,
		cancel:         cancel,
		webrtcProxies:  make(map[string]*WebRTCProxy),
	}
}

func (p *Publisher) Start() error {
	p.logger.Info("Starting publisher",
		zap.String("server_url", p.serverURL),
		zap.String("mediamtx_url", p.mediamtxURL),
		zap.Strings("managed_streams", p.managedStreams))

	for {
		select {
		case <-p.ctx.Done():
			return nil
		default:
			if err := p.connectAndRun(); err != nil {
				p.logger.Error("Connection error", zap.Error(err))
				select {
				case <-p.ctx.Done():
					return nil
				case <-time.After(p.reconnectDelay):
					p.logger.Info("Attempting to reconnect...")
					continue
				}
			}
		}
	}
}

func (p *Publisher) Stop() {
	p.logger.Info("Stopping publisher")
	p.cancel()

	// Cleanup all WebRTC proxies
	p.proxiesMutex.Lock()
	for viewerID, proxy := range p.webrtcProxies {
		proxy.Close()
		p.logger.Info("Closed WebRTC proxy", zap.String("viewer_id", viewerID))
	}
	p.webrtcProxies = make(map[string]*WebRTCProxy)
	p.proxiesMutex.Unlock()

	if p.conn != nil {
		p.conn.Close()
	}
}

func (p *Publisher) connectAndRun() error {
	u, err := url.Parse(p.serverURL)
	if err != nil {
		return fmt.Errorf("invalid server URL: %w", err)
	}

	p.logger.Info("Connecting to server", zap.String("url", u.String()))

	conn, _, err := websocket.DefaultDialer.Dial(u.String(), nil)
	if err != nil {
		return fmt.Errorf("failed to connect to server: %w", err)
	}
	p.conn = conn

	defer func() {
		p.conn.Close()
		p.conn = nil
	}()

	p.logger.Info("Connected to server")

	// Register as publisher
	if err := p.sendMessage(WebSocketMessage{
		Type: PublisherRegister,
		Data: nil,
	}); err != nil {
		return fmt.Errorf("failed to register as publisher: %w", err)
	}

	// Send managed streams
	if err := p.sendStreamsUpdate(); err != nil {
		return fmt.Errorf("failed to send streams update: %w", err)
	}

	// Start message handling loop
	return p.messageLoop()
}

func (p *Publisher) sendStreamsUpdate() error {
	// For now, we use the configured managed streams
	// In a real implementation, you might query MediaMTX API to get available streams
	data := PublisherStreamsData{
		Streams: p.managedStreams,
	}

	return p.sendMessage(WebSocketMessage{
		Type: PublisherStreams,
		Data: data,
	})
}

func (p *Publisher) messageLoop() error {
	for {
		select {
		case <-p.ctx.Done():
			return nil
		default:
			var msg WebSocketMessage
			if err := p.conn.ReadJSON(&msg); err != nil {
				if websocket.IsUnexpectedCloseError(err, websocket.CloseGoingAway, websocket.CloseAbnormalClosure) {
					return fmt.Errorf("websocket read error: %w", err)
				}
				return nil
			}

			// Reduced logging - only log important message types
			if msg.Type == WebRTCOffer || msg.Type == PublisherAnswer {
				p.logger.Info("Received message", zap.String("type", string(msg.Type)))
			}

			if err := p.handleMessage(msg); err != nil {
				p.logger.Error("Error handling message", zap.Error(err))
			}
		}
	}
}

func (p *Publisher) handleMessage(msg WebSocketMessage) error {
	switch msg.Type {
	case WebRTCOffer:
		return p.handleWebRTCOffer(msg.Data)
	case ViewerICECandidate:
		return p.handleViewerICECandidate(msg.Data)
	default:
		p.logger.Warn("Unknown message type", zap.String("type", string(msg.Type)))
	}
	return nil
}

func (p *Publisher) handleWebRTCOffer(data interface{}) error {
	dataBytes, _ := json.Marshal(data)
	var offerData WebRTCOfferData
	if err := json.Unmarshal(dataBytes, &offerData); err != nil {
		return fmt.Errorf("failed to parse WebRTC offer data: %w", err)
	}

	p.logger.Info("Received WebRTC offer",
		zap.String("stream_path", offerData.StreamPath),
		zap.String("viewer_id", offerData.ViewerID))

	// Create WebRTC proxy for this viewer
	proxy := NewWebRTCProxy(p.logger)

	// Set up callbacks
	proxy.SetCallbacks(
		// onICECandidate - send to viewer via server
		func(candidate webrtc.ICECandidate) {
			p.sendICECandidate(offerData.ViewerID, candidate)
		},
		// onAnswer - send answer to viewer
		func(sdp string) {
			p.sendAnswer(offerData.ViewerID, sdp)
		},
		// onError - cleanup proxy
		func(err error) {
			p.logger.Error("WebRTC proxy error",
				zap.String("viewer_id", offerData.ViewerID),
				zap.Error(err))
			p.removeProxy(offerData.ViewerID)
		},
	)

	// Store proxy
	p.proxiesMutex.Lock()
	p.webrtcProxies[offerData.ViewerID] = proxy
	p.proxiesMutex.Unlock()

	// Parse offer SDP
	offer := webrtc.SessionDescription{
		Type: webrtc.SDPTypeOffer,
		SDP:  offerData.SDP,
	}

	// Handle viewer offer (this will trigger trickle ICE)
	if err := proxy.HandleViewerOffer(offer, offerData.StreamPath); err != nil {
		p.logger.Error("Failed to handle viewer offer",
			zap.String("stream_path", offerData.StreamPath),
			zap.String("viewer_id", offerData.ViewerID),
			zap.Error(err))
		p.removeProxy(offerData.ViewerID)
		return err
	}

	return nil
}

func (p *Publisher) handleViewerICECandidate(data interface{}) error {
	dataBytes, _ := json.Marshal(data)
	var candidateData ICECandidateData
	if err := json.Unmarshal(dataBytes, &candidateData); err != nil {
		return fmt.Errorf("failed to parse ICE candidate data: %w", err)
	}

	// Reduced logging for ICE candidates

	// Find proxy for this viewer
	p.proxiesMutex.RLock()
	proxy, exists := p.webrtcProxies[candidateData.ViewerID]
	p.proxiesMutex.RUnlock()

	if !exists {
		return fmt.Errorf("no WebRTC proxy found for viewer %s", candidateData.ViewerID)
	}

	// Add ICE candidate to proxy
	return proxy.AddViewerICECandidate(candidateData.Candidate)
}

func (p *Publisher) sendAnswer(viewerID, sdp string) {
	msg := WebSocketMessage{
		Type: PublisherAnswer,
		Data: PublisherAnswerData{
			ViewerID: viewerID,
			SDP:      sdp,
		},
	}

	if err := p.sendMessage(msg); err != nil {
		p.logger.Error("Failed to send answer",
			zap.String("viewer_id", viewerID),
			zap.Error(err))
	}
}

func (p *Publisher) sendICECandidate(viewerID string, candidate webrtc.ICECandidate) {
	candidateInit := candidate.ToJSON()
	msg := WebSocketMessage{
		Type: PublisherICECandidate,
		Data: ICECandidateData{
			ViewerID:      viewerID,
			Candidate:     candidateInit.Candidate,
			SDPMid:        *candidateInit.SDPMid,
			SDPMLineIndex: int(*candidateInit.SDPMLineIndex),
		},
	}

	if err := p.sendMessage(msg); err != nil {
		p.logger.Error("Failed to send ICE candidate",
			zap.String("viewer_id", viewerID),
			zap.Error(err))
	}
}

func (p *Publisher) removeProxy(viewerID string) {
	p.proxiesMutex.Lock()
	defer p.proxiesMutex.Unlock()

	if proxy, exists := p.webrtcProxies[viewerID]; exists {
		proxy.Close()
		delete(p.webrtcProxies, viewerID)
		p.logger.Info("Removed WebRTC proxy", zap.String("viewer_id", viewerID))
	}
}

func (p *Publisher) createWHEPSession(streamPath, offerSDP string) (string, error) {
	// MediaMTX WHEP endpoint URL
	whepURL := fmt.Sprintf("%s/%s/whep", p.mediamtxURL, streamPath)

	p.logger.Info("Creating WHEP session",
		zap.String("url", whepURL),
		zap.String("stream_path", streamPath))

	// Create HTTP request with offer SDP
	req, err := http.NewRequest("POST", whepURL, strings.NewReader(offerSDP))
	if err != nil {
		return "", fmt.Errorf("failed to create HTTP request: %w", err)
	}

	req.Header.Set("Content-Type", "application/sdp")
	req.Header.Set("Accept", "application/sdp")

	// Send request to MediaMTX
	client := &http.Client{Timeout: 10 * time.Second}
	resp, err := client.Do(req)
	if err != nil {
		return "", fmt.Errorf("failed to send WHEP request: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusCreated {
		body, _ := io.ReadAll(resp.Body)
		return "", fmt.Errorf("WHEP request failed with status %d: %s", resp.StatusCode, string(body))
	}

	// Read answer SDP from response
	answerSDP, err := io.ReadAll(resp.Body)
	if err != nil {
		return "", fmt.Errorf("failed to read answer SDP: %w", err)
	}

	p.logger.Info("WHEP session created successfully",
		zap.String("stream_path", streamPath),
		zap.String("location", resp.Header.Get("Location")))

	return string(answerSDP), nil
}

func (p *Publisher) sendMessage(msg WebSocketMessage) error {
	if p.conn == nil {
		return fmt.Errorf("websocket connection not established")
	}

	p.logger.Debug("Sending message", zap.String("type", string(msg.Type)))

	// 🔒 Protect WebSocket writes with mutex to prevent concurrent write panic
	p.connMutex.Lock()
	defer p.connMutex.Unlock()

	return p.conn.WriteJSON(msg)
}

// queryMediaMTXStreams queries MediaMTX API to get available streams
func (p *Publisher) queryMediaMTXStreams() ([]string, error) {
	apiURL := fmt.Sprintf("%s/v3/paths/list", p.mediamtxURL)

	resp, err := http.Get(apiURL)
	if err != nil {
		return nil, fmt.Errorf("failed to query MediaMTX API: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("MediaMTX API returned status %d", resp.StatusCode)
	}

	var listResp MediaMTXListResponse
	if err := json.NewDecoder(resp.Body).Decode(&listResp); err != nil {
		return nil, fmt.Errorf("failed to decode MediaMTX response: %w", err)
	}

	var streams []string
	for pathName, pathInfo := range listResp.Items {
		if pathInfo.SourceReady && len(pathInfo.Tracks) > 0 {
			streams = append(streams, pathName)
		}
	}

	return streams, nil
}

func main() {
	// Configuration - in a real deployment, these would come from environment variables or config file
	serverURL := "ws://localhost:8080/ws"
	mediamtxURL := "http://localhost:8889"
	managedStreams := []string{"stream-1"} // Default managed streams

	// Override from environment variables if available
	if envServerURL := os.Getenv("CAM_SERVER_URL"); envServerURL != "" {
		serverURL = envServerURL
	}
	if envMediaMTXURL := os.Getenv("MEDIAMTX_URL"); envMediaMTXURL != "" {
		mediamtxURL = envMediaMTXURL
	}

	publisher := NewPublisher(serverURL, mediamtxURL, managedStreams)

	// Handle graceful shutdown
	c := make(chan os.Signal, 1)
	signal.Notify(c, os.Interrupt, syscall.SIGTERM)

	go func() {
		<-c
		publisher.logger.Info("Received shutdown signal")
		publisher.Stop()
	}()

	// Start publisher
	if err := publisher.Start(); err != nil {
		publisher.logger.Fatal("Publisher failed", zap.Error(err))
	}

	publisher.logger.Info("Publisher stopped")
}
