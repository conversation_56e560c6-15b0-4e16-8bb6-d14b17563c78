# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is a camera streaming project with a React Router frontend application. The project appears to be in early development with empty `server/` and `publisher/` directories and a fully functional React Router viewer application.

## Architecture

- **viewer/**: React Router v7 application with TypeScript and TailwindCSS
  - Modern SSR-capable frontend using React Router v7
  - TypeScript by default with Vite as the build tool
  - TailwindCSS for styling with dark mode support
  - Uses <PERSON><PERSON> for state management and Lucide React for icons

## Development Commands

### Viewer Application (React Router)
```bash
cd viewer

# Development
npm run dev          # Start development server at http://localhost:5173

# Build and deployment
npm run build        # Create production build
npm run start        # Start production server
npm run typecheck    # Run TypeScript type checking

# Docker
docker build -t cam-streaming-viewer .
docker run -p 3000:3000 cam-streaming-viewer
```

## Key Technologies

- **Frontend**: React Router v7, React 19, TypeScript, TailwindCSS v4
- **Build Tool**: Vite with React Router dev tools
- **State Management**: <PERSON><PERSON>
- **Styling**: TailwindCSS with dark mode
- **Icons**: Lucide React
- **Deployment**: Docker-ready with multi-stage builds

## File Structure

```
cam-streaming/
├── viewer/          # React Router frontend application
│   ├── app/         # Application source code
│   │   ├── routes/  # React Router routes
│   │   ├── welcome/ # Welcome page component
│   │   └── lib/     # Utility functions
│   ├── public/      # Static assets
│   └── build/       # Production build output (after npm run build)
├── server/          # Empty - backend application (planned)
└── publisher/       # Empty - streaming publisher (planned)
```

## Development Notes

- The project uses React Router v7's file-based routing system
- TypeScript configuration is strict with proper type generation
- TailwindCSS v4 is configured with Vite plugin
- Production builds use multi-stage Docker containers for optimization
- The viewer app is a standard React Router template that can be customized for camera streaming functionality
- you already write build script, I will run it to build manually