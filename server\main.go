package main

import (
	"encoding/json"
	"log"
	"os"
	"sync"

	"github.com/gofiber/fiber/v2"
	"github.com/gofiber/fiber/v2/middleware/cors"
	"github.com/gofiber/websocket/v2"
	"github.com/google/uuid"
	"go.uber.org/zap"
)

type MessageType string

const (
	// Publisher messages
	PublisherRegister     MessageType = "publisher_register"
	PublisherStreams      MessageType = "publisher_streams"
	PublisherAnswer       MessageType = "publisher_answer"
	PublisherICECandidate MessageType = "publisher_ice_candidate"

	// Viewer messages
	ViewerRegister      MessageType = "viewer_register"
	ViewerOffer         MessageType = "viewer_offer"
	ViewerICECandidate  MessageType = "viewer_ice_candidate"

	// Server messages
	StreamsList         MessageType = "streams_list"
	StreamStatusUpdate  MessageType = "stream_status_update"
	WebRTCOffer         MessageType = "webrtc_offer"
	WebRTCAnswer        MessageType = "webrtc_answer"
	ICECandidate        MessageType = "ice_candidate"
)

type Stream struct {
	Path     string `json:"path"`
	Name     string `json:"name"`
	Active   bool   `json:"active"`
	PublisherID string `json:"-"`
}

type WebSocketMessage struct {
	Type MessageType `json:"type"`
	Data interface{} `json:"data"`
}

type PublisherStreamsData struct {
	Streams []string `json:"streams"`
}

type ViewerOfferData struct {
	StreamPath string `json:"stream_path"`
	SDP        string `json:"sdp"`
	ViewerID   string `json:"viewer_id"`
}

type WebRTCOfferData struct {
	StreamPath string `json:"stream_path"`
	SDP        string `json:"sdp"`
	ViewerID   string `json:"viewer_id"`
}

type PublisherAnswerData struct {
	ViewerID string `json:"viewer_id"`
	SDP      string `json:"sdp"`
}

type WebRTCAnswerData struct {
	SDP string `json:"sdp"`
}

type ICECandidateData struct {
	ViewerID      string `json:"viewer_id"`
	Candidate     string `json:"candidate"`
	SDPMid        string `json:"sdp_mid"`
	SDPMLineIndex int    `json:"sdp_mline_index"`
}

type Connection struct {
	ID       string
	Conn     *websocket.Conn
	Type     string // "publisher" or "viewer"
	StreamPaths []string // for publishers, the streams they manage
}

type Server struct {
	logger      *zap.Logger
	connections map[string]*Connection
	streams     map[string]*Stream
	mutex       sync.RWMutex
}

func NewServer() *Server {
	logger, _ := zap.NewProduction()

	// Initialize with hardcoded stream
	streams := map[string]*Stream{
		"stream-1": {
			Path:   "stream-1",
			Name:   "Cam cân heo",
			Active: false,
		},
	}

	server := &Server{
		logger:      logger,
		connections: make(map[string]*Connection),
		streams:     streams,
	}

	logger.Info("Server initialized with streams",
		zap.Int("stream_count", len(streams)))

	return server
}

func (s *Server) handleWebSocket(c *websocket.Conn) {
	connID := uuid.New().String()
	conn := &Connection{
		ID:   connID,
		Conn: c,
	}

	s.mutex.Lock()
	s.connections[connID] = conn
	s.mutex.Unlock()

	s.logger.Info("New WebSocket connection", zap.String("connection_id", connID))

	defer func() {
		s.mutex.Lock()
		connectionType := "unknown"
		if conn.Type != "" {
			connectionType = conn.Type
		}
		s.logger.Info("Cleaning up connection",
			zap.String("connection_id", connID),
			zap.String("type", connectionType))
		delete(s.connections, connID)

		// If this was a publisher, mark its streams as inactive
		shouldBroadcast := false
		if conn.Type == "publisher" {
			for _, streamPath := range conn.StreamPaths {
				if stream, exists := s.streams[streamPath]; exists {
					stream.Active = false
					stream.PublisherID = ""
					shouldBroadcast = true
					s.logger.Info("Stream marked as inactive",
						zap.String("stream_path", streamPath),
						zap.String("publisher_id", connID))
				}
			}
		}
		s.mutex.Unlock()

		// Broadcast outside the lock to avoid deadlock
		if shouldBroadcast {
			s.broadcastStreamsUpdate()
		}

		c.Close()
		s.logger.Info("WebSocket connection closed", zap.String("connection_id", connID))
	}()

	for {
		var msg WebSocketMessage
		if err := c.ReadJSON(&msg); err != nil {
			if websocket.IsUnexpectedCloseError(err, websocket.CloseGoingAway, websocket.CloseAbnormalClosure, websocket.CloseNormalClosure) {
				s.logger.Error("Unexpected WebSocket close",
					zap.String("connection_id", connID),
					zap.Error(err))
			} else {
				s.logger.Debug("WebSocket connection closed normally",
					zap.String("connection_id", connID))
			}
			break
		}

		s.logger.Info("Received message",
			zap.String("connection_id", connID),
			zap.String("type", string(msg.Type)),
			zap.Any("data", msg.Data))

		if err := s.handleMessage(connID, msg); err != nil {
			s.logger.Error("Error handling message",
				zap.String("connection_id", connID),
				zap.Error(err))
		}
	}
}

func (s *Server) handleMessage(connID string, msg WebSocketMessage) error {
	s.mutex.Lock()
	_, exists := s.connections[connID]
	if !exists {
		s.mutex.Unlock()
		return fiber.NewError(404, "Connection not found")
	}
	s.mutex.Unlock()

	switch msg.Type {
	case PublisherRegister:
		s.logger.Debug("Handling publisher register", zap.String("connection_id", connID))
		s.handlePublisherRegister(connID)
	case PublisherStreams:
		s.logger.Debug("Handling publisher streams", zap.String("connection_id", connID))
		s.handlePublisherStreams(connID, msg.Data)
	case PublisherAnswer:
		s.logger.Debug("Handling publisher answer", zap.String("connection_id", connID))
		s.handlePublisherAnswer(connID, msg.Data)
	case PublisherICECandidate:
		s.logger.Debug("Handling publisher ICE candidate", zap.String("connection_id", connID))
		s.handlePublisherICECandidate(connID, msg.Data)
	case ViewerRegister:
		s.logger.Info("Handling viewer register", zap.String("connection_id", connID))
		s.handleViewerRegister(connID)
	case ViewerOffer:
		s.logger.Debug("Handling viewer offer", zap.String("connection_id", connID))
		s.handleViewerOffer(connID, msg.Data)
	case ViewerICECandidate:
		s.logger.Debug("Handling viewer ICE candidate", zap.String("connection_id", connID))
		s.handleViewerICECandidate(connID, msg.Data)
	default:
		s.logger.Warn("Unknown message type",
			zap.String("connection_id", connID),
			zap.String("type", string(msg.Type)))
	}

	return nil
}

func (s *Server) handlePublisherRegister(connID string) {
	s.mutex.Lock()
	defer s.mutex.Unlock()

	if conn, exists := s.connections[connID]; exists {
		conn.Type = "publisher"
		s.logger.Info("Publisher registered", zap.String("connection_id", connID))
	}
}

func (s *Server) handlePublisherStreams(connID string, data interface{}) {
	dataBytes, _ := json.Marshal(data)
	var streamsData PublisherStreamsData
	if err := json.Unmarshal(dataBytes, &streamsData); err != nil {
		s.logger.Error("Error parsing publisher streams data", zap.Error(err))
		return
	}

	s.mutex.Lock()
	conn, exists := s.connections[connID]
	if !exists {
		s.mutex.Unlock()
		return
	}

	conn.StreamPaths = streamsData.Streams

	// Mark streams as active if they exist in our stream list
	updatedCount := 0
	for _, streamPath := range streamsData.Streams {
		if stream, exists := s.streams[streamPath]; exists {
			stream.Active = true
			stream.PublisherID = connID
			updatedCount++
			s.logger.Info("Stream marked as active",
				zap.String("stream_path", streamPath),
				zap.String("publisher_id", connID))
		}
	}

	s.logger.Info("Publisher streams updated",
		zap.String("connection_id", connID),
		zap.Strings("streams", streamsData.Streams),
		zap.Int("updated_count", updatedCount))

	// Unlock before broadcast to avoid deadlock
	s.mutex.Unlock()

	// Broadcast streams update to all viewers
	s.broadcastStreamsUpdate()
}

func (s *Server) handlePublisherAnswer(connID string, data interface{}) {
	dataBytes, _ := json.Marshal(data)
	var answerData PublisherAnswerData
	if err := json.Unmarshal(dataBytes, &answerData); err != nil {
		s.logger.Error("Error parsing publisher answer data", zap.Error(err))
		return
	}

	s.logger.Info("Received answer from publisher",
		zap.String("publisher_id", connID),
		zap.String("viewer_id", answerData.ViewerID))

	// Send answer to the specific viewer
	s.sendToViewer(answerData.ViewerID, WebSocketMessage{
		Type: WebRTCAnswer,
		Data: WebRTCAnswerData{
			SDP: answerData.SDP,
		},
	})
}

func (s *Server) handlePublisherICECandidate(connID string, data interface{}) {
	dataBytes, _ := json.Marshal(data)
	var candidateData ICECandidateData
	if err := json.Unmarshal(dataBytes, &candidateData); err != nil {
		s.logger.Error("Error parsing publisher ICE candidate data", zap.Error(err))
		return
	}

	s.logger.Debug("Received ICE candidate from publisher",
		zap.String("publisher_id", connID),
		zap.String("viewer_id", candidateData.ViewerID),
		zap.String("candidate", candidateData.Candidate))

	// Send ICE candidate to the specific viewer
	s.sendToViewer(candidateData.ViewerID, WebSocketMessage{
		Type: ICECandidate,
		Data: ICECandidateData{
			ViewerID:      candidateData.ViewerID,
			Candidate:     candidateData.Candidate,
			SDPMid:        candidateData.SDPMid,
			SDPMLineIndex: candidateData.SDPMLineIndex,
		},
	})
}

func (s *Server) handleViewerRegister(connID string) {
	s.mutex.Lock()
	conn, exists := s.connections[connID]
	if !exists {
		s.mutex.Unlock()
		return
	}

	// Prevent double registration
	if conn.Type == "viewer" {
		s.mutex.Unlock()
		s.logger.Debug("Viewer already registered, ignoring duplicate register",
			zap.String("connection_id", connID))
		return
	}

	conn.Type = "viewer"
	s.mutex.Unlock()

	s.logger.Info("Viewer registered", zap.String("connection_id", connID))

	// Send current streams list to the new viewer
	s.sendStreamsList(connID)
}

func (s *Server) handleViewerOffer(connID string, data interface{}) {
	dataBytes, _ := json.Marshal(data)
	var offerData ViewerOfferData
	if err := json.Unmarshal(dataBytes, &offerData); err != nil {
		s.logger.Error("Error parsing viewer offer data", zap.Error(err))
		return
	}

	s.logger.Info("Received offer from viewer",
		zap.String("viewer_id", connID),
		zap.String("stream_path", offerData.StreamPath))

	// Find the publisher that manages this stream
	s.mutex.RLock()
	var publisherID string
	if stream, exists := s.streams[offerData.StreamPath]; exists && stream.Active {
		publisherID = stream.PublisherID
	}
	s.mutex.RUnlock()

	if publisherID == "" {
		s.logger.Error("No active publisher found for stream",
			zap.String("stream_path", offerData.StreamPath))
		return
	}

	// Send offer to the publisher
	s.sendToPublisher(publisherID, WebSocketMessage{
		Type: WebRTCOffer,
		Data: WebRTCOfferData{
			StreamPath: offerData.StreamPath,
			SDP:        offerData.SDP,
			ViewerID:   connID,
		},
	})
}

func (s *Server) handleViewerICECandidate(connID string, data interface{}) {
	dataBytes, _ := json.Marshal(data)
	var candidateData ICECandidateData
	if err := json.Unmarshal(dataBytes, &candidateData); err != nil {
		s.logger.Error("Error parsing viewer ICE candidate data", zap.Error(err))
		return
	}

	s.logger.Debug("Received ICE candidate from viewer",
		zap.String("viewer_id", connID),
		zap.String("candidate", candidateData.Candidate))

	// Find the publisher that is handling this viewer's stream
	// We need to find the active stream that this viewer is connected to
	// For now, we'll assume there's only one active publisher and route to it
	// In a more complex system, we'd need to track viewer-to-publisher mappings
	s.mutex.RLock()
	var publisherID string
	for _, conn := range s.connections {
		if conn.Type == "publisher" {
			publisherID = conn.ID
			break
		}
	}
	s.mutex.RUnlock()

	if publisherID == "" {
		s.logger.Error("No active publisher found for routing ICE candidate from viewer",
			zap.String("viewer_id", connID))
		return
	}

	// Send ICE candidate to the publisher with viewer ID
	s.sendToPublisher(publisherID, WebSocketMessage{
		Type: ViewerICECandidate,
		Data: ICECandidateData{
			ViewerID:      connID,
			Candidate:     candidateData.Candidate,
			SDPMid:        candidateData.SDPMid,
			SDPMLineIndex: candidateData.SDPMLineIndex,
		},
	})
}

func (s *Server) sendStreamsList(viewerID string) {
	s.mutex.RLock()
	streams := make([]*Stream, 0, len(s.streams))
	for _, stream := range s.streams {
		streams = append(streams, stream)
	}
	activeCount := 0
	for _, stream := range streams {
		if stream.Active {
			activeCount++
		}
	}
	s.mutex.RUnlock()

	s.logger.Info("Sending streams list to viewer",
		zap.String("viewer_id", viewerID),
		zap.Int("stream_count", len(streams)),
		zap.Int("active_count", activeCount))

	msg := WebSocketMessage{
		Type: StreamsList,
		Data: streams,
	}

	s.sendToConnection(viewerID, msg)
}

func (s *Server) broadcastStreamsUpdate() {
	s.mutex.RLock()
	streams := make([]*Stream, 0, len(s.streams))
	for _, stream := range s.streams {
		streams = append(streams, stream)
	}

	msg := WebSocketMessage{
		Type: StreamsList,
		Data: streams,
	}

	// Collect viewer IDs first
	var viewerIDs []string
	for _, conn := range s.connections {
		if conn.Type == "viewer" {
			viewerIDs = append(viewerIDs, conn.ID)
		}
	}
	s.mutex.RUnlock()

	s.logger.Info("Broadcasting streams update",
		zap.Int("viewer_count", len(viewerIDs)),
		zap.Int("stream_count", len(streams)))

	// Send to all viewers outside the lock
	for _, viewerID := range viewerIDs {
		go s.sendToConnection(viewerID, msg)
	}
}

func (s *Server) sendToViewer(viewerID string, msg WebSocketMessage) {
	s.sendToConnection(viewerID, msg)
}

func (s *Server) sendToPublisher(publisherID string, msg WebSocketMessage) {
	s.sendToConnection(publisherID, msg)
}

func (s *Server) sendToConnection(connID string, msg WebSocketMessage) {
	s.mutex.RLock()
	conn, exists := s.connections[connID]
	s.mutex.RUnlock()

	if !exists {
		s.logger.Error("Connection not found for sending message",
			zap.String("connection_id", connID))
		return
	}

	if err := conn.Conn.WriteJSON(msg); err != nil {
		s.logger.Error("Error sending message to connection",
			zap.String("connection_id", connID),
			zap.Error(err))
	} else {
		s.logger.Info("Message sent to connection",
			zap.String("connection_id", connID),
			zap.String("message_type", string(msg.Type)))
	}
}

func main() {
	server := NewServer()
	defer server.logger.Sync()

	// Get port from environment variable, fallback to 8080
	port := os.Getenv("CAM_SERVER_PORT")
	if port == "" {
		port = "8080"
	}

	app := fiber.New(fiber.Config{
		ErrorHandler: func(c *fiber.Ctx, err error) error {
			code := fiber.StatusInternalServerError
			if e, ok := err.(*fiber.Error); ok {
				code = e.Code
			}
			server.logger.Error("HTTP error",
				zap.Int("status", code),
				zap.String("path", c.Path()),
				zap.Error(err))
			return c.Status(code).JSON(fiber.Map{"error": err.Error()})
		},
	})

	// CORS middleware
	app.Use(cors.New(cors.Config{
		AllowOrigins: "*",
		AllowMethods: "GET,POST,PUT,DELETE,OPTIONS",
		AllowHeaders: "Origin,Content-Type,Accept,Authorization",
	}))

	// Health check
	app.Get("/health", func(c *fiber.Ctx) error {
		return c.JSON(fiber.Map{"status": "ok"})
	})

	// WebSocket upgrade middleware
	app.Use("/ws", func(c *fiber.Ctx) error {
		if websocket.IsWebSocketUpgrade(c) {
			c.Locals("allowed", true)
			return c.Next()
		}
		return fiber.ErrUpgradeRequired
	})

	// WebSocket endpoint
	app.Get("/ws", websocket.New(server.handleWebSocket))

	server.logger.Info("Starting server", zap.String("port", port))
	log.Fatal(app.Listen(":" + port))
}